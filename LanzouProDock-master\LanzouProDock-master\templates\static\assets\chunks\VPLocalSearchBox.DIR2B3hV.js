var Ct=Object.defineProperty;var It=(o,e,t)=>e in o?Ct(o,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[e]=t;var Oe=(o,e,t)=>(It(o,typeof e!="symbol"?e+"":e,t),t);import{X as Dt,s as oe,v as $e,ai as kt,aj as Ot,d as Rt,G as xe,ak as tt,h as Fe,al as _t,am as Mt,x as Lt,an as Pt,y as Re,R as de,Q as Ee,ao as zt,ap as Bt,Y as Vt,U as $t,aq as Wt,o as ee,b as Kt,j as k,a1 as Jt,k as j,ar as Ut,as as jt,at as Gt,c as re,n as rt,e as Se,E as at,F as nt,a as ve,t as pe,au as Qt,p as qt,l as Ht,av as it,aw as Yt,a7 as Zt,ad as Xt,ax as er,_ as tr}from"./framework.D42TvibZ.js";import{u as rr,c as ar}from"./theme.5rVOLq5J.js";const nr={root:()=>Dt(()=>import("./@localSearchIndexroot.Dmo7JADz.js"),[])};/*!
* tabbable 6.2.0
* @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
*/var yt=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"],Ne=yt.join(","),mt=typeof Element>"u",ue=mt?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,Ce=!mt&&Element.prototype.getRootNode?function(o){var e;return o==null||(e=o.getRootNode)===null||e===void 0?void 0:e.call(o)}:function(o){return o==null?void 0:o.ownerDocument},Ie=function o(e,t){var r;t===void 0&&(t=!0);var n=e==null||(r=e.getAttribute)===null||r===void 0?void 0:r.call(e,"inert"),a=n===""||n==="true",i=a||t&&e&&o(e.parentNode);return i},ir=function(e){var t,r=e==null||(t=e.getAttribute)===null||t===void 0?void 0:t.call(e,"contenteditable");return r===""||r==="true"},gt=function(e,t,r){if(Ie(e))return[];var n=Array.prototype.slice.apply(e.querySelectorAll(Ne));return t&&ue.call(e,Ne)&&n.unshift(e),n=n.filter(r),n},bt=function o(e,t,r){for(var n=[],a=Array.from(e);a.length;){var i=a.shift();if(!Ie(i,!1))if(i.tagName==="SLOT"){var s=i.assignedElements(),u=s.length?s:i.children,l=o(u,!0,r);r.flatten?n.push.apply(n,l):n.push({scopeParent:i,candidates:l})}else{var h=ue.call(i,Ne);h&&r.filter(i)&&(t||!e.includes(i))&&n.push(i);var d=i.shadowRoot||typeof r.getShadowRoot=="function"&&r.getShadowRoot(i),v=!Ie(d,!1)&&(!r.shadowRootFilter||r.shadowRootFilter(i));if(d&&v){var y=o(d===!0?i.children:d.children,!0,r);r.flatten?n.push.apply(n,y):n.push({scopeParent:i,candidates:y})}else a.unshift.apply(a,i.children)}}return n},wt=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},se=function(e){if(!e)throw new Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||ir(e))&&!wt(e)?0:e.tabIndex},or=function(e,t){var r=se(e);return r<0&&t&&!wt(e)?0:r},sr=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},xt=function(e){return e.tagName==="INPUT"},ur=function(e){return xt(e)&&e.type==="hidden"},lr=function(e){var t=e.tagName==="DETAILS"&&Array.prototype.slice.apply(e.children).some(function(r){return r.tagName==="SUMMARY"});return t},cr=function(e,t){for(var r=0;r<e.length;r++)if(e[r].checked&&e[r].form===t)return e[r]},fr=function(e){if(!e.name)return!0;var t=e.form||Ce(e),r=function(s){return t.querySelectorAll('input[type="radio"][name="'+s+'"]')},n;if(typeof window<"u"&&typeof window.CSS<"u"&&typeof window.CSS.escape=="function")n=r(window.CSS.escape(e.name));else try{n=r(e.name)}catch(i){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",i.message),!1}var a=cr(n,e.form);return!a||a===e},hr=function(e){return xt(e)&&e.type==="radio"},dr=function(e){return hr(e)&&!fr(e)},vr=function(e){var t,r=e&&Ce(e),n=(t=r)===null||t===void 0?void 0:t.host,a=!1;if(r&&r!==e){var i,s,u;for(a=!!((i=n)!==null&&i!==void 0&&(s=i.ownerDocument)!==null&&s!==void 0&&s.contains(n)||e!=null&&(u=e.ownerDocument)!==null&&u!==void 0&&u.contains(e));!a&&n;){var l,h,d;r=Ce(n),n=(l=r)===null||l===void 0?void 0:l.host,a=!!((h=n)!==null&&h!==void 0&&(d=h.ownerDocument)!==null&&d!==void 0&&d.contains(n))}}return a},ot=function(e){var t=e.getBoundingClientRect(),r=t.width,n=t.height;return r===0&&n===0},pr=function(e,t){var r=t.displayCheck,n=t.getShadowRoot;if(getComputedStyle(e).visibility==="hidden")return!0;var a=ue.call(e,"details>summary:first-of-type"),i=a?e.parentElement:e;if(ue.call(i,"details:not([open]) *"))return!0;if(!r||r==="full"||r==="legacy-full"){if(typeof n=="function"){for(var s=e;e;){var u=e.parentElement,l=Ce(e);if(u&&!u.shadowRoot&&n(u)===!0)return ot(e);e.assignedSlot?e=e.assignedSlot:!u&&l!==e.ownerDocument?e=l.host:e=u}e=s}if(vr(e))return!e.getClientRects().length;if(r!=="legacy-full")return!0}else if(r==="non-zero-area")return ot(e);return!1},yr=function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if(t.tagName==="FIELDSET"&&t.disabled){for(var r=0;r<t.children.length;r++){var n=t.children.item(r);if(n.tagName==="LEGEND")return ue.call(t,"fieldset[disabled] *")?!0:!n.contains(e)}return!0}t=t.parentElement}return!1},De=function(e,t){return!(t.disabled||Ie(t)||ur(t)||pr(t,e)||lr(t)||yr(t))},We=function(e,t){return!(dr(t)||se(t)<0||!De(e,t))},mr=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},gr=function o(e){var t=[],r=[];return e.forEach(function(n,a){var i=!!n.scopeParent,s=i?n.scopeParent:n,u=or(s,i),l=i?o(n.candidates):s;u===0?i?t.push.apply(t,l):t.push(s):r.push({documentOrder:a,tabIndex:u,item:n,isScope:i,content:l})}),r.sort(sr).reduce(function(n,a){return a.isScope?n.push.apply(n,a.content):n.push(a.content),n},[]).concat(t)},br=function(e,t){t=t||{};var r;return t.getShadowRoot?r=bt([e],t.includeContainer,{filter:We.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:mr}):r=gt(e,t.includeContainer,We.bind(null,t)),gr(r)},wr=function(e,t){t=t||{};var r;return t.getShadowRoot?r=bt([e],t.includeContainer,{filter:De.bind(null,t),flatten:!0,getShadowRoot:t.getShadowRoot}):r=gt(e,t.includeContainer,De.bind(null,t)),r},le=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return ue.call(e,Ne)===!1?!1:We(t,e)},xr=yt.concat("iframe").join(","),_e=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return ue.call(e,xr)===!1?!1:De(t,e)};/*!
* focus-trap 7.5.4
* @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
*/function st(o,e){var t=Object.keys(o);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(o);e&&(r=r.filter(function(n){return Object.getOwnPropertyDescriptor(o,n).enumerable})),t.push.apply(t,r)}return t}function ut(o){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?st(Object(t),!0).forEach(function(r){Fr(o,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(t)):st(Object(t)).forEach(function(r){Object.defineProperty(o,r,Object.getOwnPropertyDescriptor(t,r))})}return o}function Fr(o,e,t){return e=Sr(e),e in o?Object.defineProperty(o,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):o[e]=t,o}function Er(o,e){if(typeof o!="object"||o===null)return o;var t=o[Symbol.toPrimitive];if(t!==void 0){var r=t.call(o,e||"default");if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(o)}function Sr(o){var e=Er(o,"string");return typeof e=="symbol"?e:String(e)}var lt={activateTrap:function(e,t){if(e.length>0){var r=e[e.length-1];r!==t&&r.pause()}var n=e.indexOf(t);n===-1||e.splice(n,1),e.push(t)},deactivateTrap:function(e,t){var r=e.indexOf(t);r!==-1&&e.splice(r,1),e.length>0&&e[e.length-1].unpause()}},Ar=function(e){return e.tagName&&e.tagName.toLowerCase()==="input"&&typeof e.select=="function"},Tr=function(e){return(e==null?void 0:e.key)==="Escape"||(e==null?void 0:e.key)==="Esc"||(e==null?void 0:e.keyCode)===27},ge=function(e){return(e==null?void 0:e.key)==="Tab"||(e==null?void 0:e.keyCode)===9},Nr=function(e){return ge(e)&&!e.shiftKey},Cr=function(e){return ge(e)&&e.shiftKey},ct=function(e){return setTimeout(e,0)},ft=function(e,t){var r=-1;return e.every(function(n,a){return t(n)?(r=a,!1):!0}),r},ye=function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return typeof e=="function"?e.apply(void 0,r):e},Ae=function(e){return e.target.shadowRoot&&typeof e.composedPath=="function"?e.composedPath()[0]:e.target},Ir=[],Dr=function(e,t){var r=(t==null?void 0:t.document)||document,n=(t==null?void 0:t.trapStack)||Ir,a=ut({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0,isKeyForward:Nr,isKeyBackward:Cr},t),i={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,delayInitialFocusTimer:void 0,recentNavEvent:void 0},s,u=function(c,f,p){return c&&c[f]!==void 0?c[f]:a[p||f]},l=function(c,f){var p=typeof(f==null?void 0:f.composedPath)=="function"?f.composedPath():void 0;return i.containerGroups.findIndex(function(C){var I=C.container,M=C.tabbableNodes;return I.contains(c)||(p==null?void 0:p.includes(I))||M.find(function(P){return P===c})})},h=function(c){var f=a[c];if(typeof f=="function"){for(var p=arguments.length,C=new Array(p>1?p-1:0),I=1;I<p;I++)C[I-1]=arguments[I];f=f.apply(void 0,C)}if(f===!0&&(f=void 0),!f){if(f===void 0||f===!1)return f;throw new Error("`".concat(c,"` was specified but was not a node, or did not return a node"))}var M=f;if(typeof f=="string"&&(M=r.querySelector(f),!M))throw new Error("`".concat(c,"` as selector refers to no known node"));return M},d=function(){var c=h("initialFocus");if(c===!1)return!1;if(c===void 0||!_e(c,a.tabbableOptions))if(l(r.activeElement)>=0)c=r.activeElement;else{var f=i.tabbableGroups[0],p=f&&f.firstTabbableNode;c=p||h("fallbackFocus")}if(!c)throw new Error("Your focus-trap needs to have at least one focusable element");return c},v=function(){if(i.containerGroups=i.containers.map(function(c){var f=br(c,a.tabbableOptions),p=wr(c,a.tabbableOptions),C=f.length>0?f[0]:void 0,I=f.length>0?f[f.length-1]:void 0,M=p.find(function(m){return le(m)}),P=p.slice().reverse().find(function(m){return le(m)}),z=!!f.find(function(m){return se(m)>0});return{container:c,tabbableNodes:f,focusableNodes:p,posTabIndexesFound:z,firstTabbableNode:C,lastTabbableNode:I,firstDomTabbableNode:M,lastDomTabbableNode:P,nextTabbableNode:function(x){var $=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,K=f.indexOf(x);return K<0?$?p.slice(p.indexOf(x)+1).find(function(Q){return le(Q)}):p.slice(0,p.indexOf(x)).reverse().find(function(Q){return le(Q)}):f[K+($?1:-1)]}}}),i.tabbableGroups=i.containerGroups.filter(function(c){return c.tabbableNodes.length>0}),i.tabbableGroups.length<=0&&!h("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times");if(i.containerGroups.find(function(c){return c.posTabIndexesFound})&&i.containerGroups.length>1)throw new Error("At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.")},y=function w(c){var f=c.activeElement;if(f)return f.shadowRoot&&f.shadowRoot.activeElement!==null?w(f.shadowRoot):f},b=function w(c){if(c!==!1&&c!==y(document)){if(!c||!c.focus){w(d());return}c.focus({preventScroll:!!a.preventScroll}),i.mostRecentlyFocusedNode=c,Ar(c)&&c.select()}},E=function(c){var f=h("setReturnFocus",c);return f||(f===!1?!1:c)},g=function(c){var f=c.target,p=c.event,C=c.isBackward,I=C===void 0?!1:C;f=f||Ae(p),v();var M=null;if(i.tabbableGroups.length>0){var P=l(f,p),z=P>=0?i.containerGroups[P]:void 0;if(P<0)I?M=i.tabbableGroups[i.tabbableGroups.length-1].lastTabbableNode:M=i.tabbableGroups[0].firstTabbableNode;else if(I){var m=ft(i.tabbableGroups,function(B){var U=B.firstTabbableNode;return f===U});if(m<0&&(z.container===f||_e(f,a.tabbableOptions)&&!le(f,a.tabbableOptions)&&!z.nextTabbableNode(f,!1))&&(m=P),m>=0){var x=m===0?i.tabbableGroups.length-1:m-1,$=i.tabbableGroups[x];M=se(f)>=0?$.lastTabbableNode:$.lastDomTabbableNode}else ge(p)||(M=z.nextTabbableNode(f,!1))}else{var K=ft(i.tabbableGroups,function(B){var U=B.lastTabbableNode;return f===U});if(K<0&&(z.container===f||_e(f,a.tabbableOptions)&&!le(f,a.tabbableOptions)&&!z.nextTabbableNode(f))&&(K=P),K>=0){var Q=K===i.tabbableGroups.length-1?0:K+1,q=i.tabbableGroups[Q];M=se(f)>=0?q.firstTabbableNode:q.firstDomTabbableNode}else ge(p)||(M=z.nextTabbableNode(f))}}else M=h("fallbackFocus");return M},S=function(c){var f=Ae(c);if(!(l(f,c)>=0)){if(ye(a.clickOutsideDeactivates,c)){s.deactivate({returnFocus:a.returnFocusOnDeactivate});return}ye(a.allowOutsideClick,c)||c.preventDefault()}},T=function(c){var f=Ae(c),p=l(f,c)>=0;if(p||f instanceof Document)p&&(i.mostRecentlyFocusedNode=f);else{c.stopImmediatePropagation();var C,I=!0;if(i.mostRecentlyFocusedNode)if(se(i.mostRecentlyFocusedNode)>0){var M=l(i.mostRecentlyFocusedNode),P=i.containerGroups[M].tabbableNodes;if(P.length>0){var z=P.findIndex(function(m){return m===i.mostRecentlyFocusedNode});z>=0&&(a.isKeyForward(i.recentNavEvent)?z+1<P.length&&(C=P[z+1],I=!1):z-1>=0&&(C=P[z-1],I=!1))}}else i.containerGroups.some(function(m){return m.tabbableNodes.some(function(x){return se(x)>0})})||(I=!1);else I=!1;I&&(C=g({target:i.mostRecentlyFocusedNode,isBackward:a.isKeyBackward(i.recentNavEvent)})),b(C||i.mostRecentlyFocusedNode||d())}i.recentNavEvent=void 0},F=function(c){var f=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;i.recentNavEvent=c;var p=g({event:c,isBackward:f});p&&(ge(c)&&c.preventDefault(),b(p))},L=function(c){if(Tr(c)&&ye(a.escapeDeactivates,c)!==!1){c.preventDefault(),s.deactivate();return}(a.isKeyForward(c)||a.isKeyBackward(c))&&F(c,a.isKeyBackward(c))},_=function(c){var f=Ae(c);l(f,c)>=0||ye(a.clickOutsideDeactivates,c)||ye(a.allowOutsideClick,c)||(c.preventDefault(),c.stopImmediatePropagation())},V=function(){if(i.active)return lt.activateTrap(n,s),i.delayInitialFocusTimer=a.delayInitialFocus?ct(function(){b(d())}):b(d()),r.addEventListener("focusin",T,!0),r.addEventListener("mousedown",S,{capture:!0,passive:!1}),r.addEventListener("touchstart",S,{capture:!0,passive:!1}),r.addEventListener("click",_,{capture:!0,passive:!1}),r.addEventListener("keydown",L,{capture:!0,passive:!1}),s},N=function(){if(i.active)return r.removeEventListener("focusin",T,!0),r.removeEventListener("mousedown",S,!0),r.removeEventListener("touchstart",S,!0),r.removeEventListener("click",_,!0),r.removeEventListener("keydown",L,!0),s},R=function(c){var f=c.some(function(p){var C=Array.from(p.removedNodes);return C.some(function(I){return I===i.mostRecentlyFocusedNode})});f&&b(d())},A=typeof window<"u"&&"MutationObserver"in window?new MutationObserver(R):void 0,O=function(){A&&(A.disconnect(),i.active&&!i.paused&&i.containers.map(function(c){A.observe(c,{subtree:!0,childList:!0})}))};return s={get active(){return i.active},get paused(){return i.paused},activate:function(c){if(i.active)return this;var f=u(c,"onActivate"),p=u(c,"onPostActivate"),C=u(c,"checkCanFocusTrap");C||v(),i.active=!0,i.paused=!1,i.nodeFocusedBeforeActivation=r.activeElement,f==null||f();var I=function(){C&&v(),V(),O(),p==null||p()};return C?(C(i.containers.concat()).then(I,I),this):(I(),this)},deactivate:function(c){if(!i.active)return this;var f=ut({onDeactivate:a.onDeactivate,onPostDeactivate:a.onPostDeactivate,checkCanReturnFocus:a.checkCanReturnFocus},c);clearTimeout(i.delayInitialFocusTimer),i.delayInitialFocusTimer=void 0,N(),i.active=!1,i.paused=!1,O(),lt.deactivateTrap(n,s);var p=u(f,"onDeactivate"),C=u(f,"onPostDeactivate"),I=u(f,"checkCanReturnFocus"),M=u(f,"returnFocus","returnFocusOnDeactivate");p==null||p();var P=function(){ct(function(){M&&b(E(i.nodeFocusedBeforeActivation)),C==null||C()})};return M&&I?(I(E(i.nodeFocusedBeforeActivation)).then(P,P),this):(P(),this)},pause:function(c){if(i.paused||!i.active)return this;var f=u(c,"onPause"),p=u(c,"onPostPause");return i.paused=!0,f==null||f(),N(),O(),p==null||p(),this},unpause:function(c){if(!i.paused||!i.active)return this;var f=u(c,"onUnpause"),p=u(c,"onPostUnpause");return i.paused=!1,f==null||f(),v(),V(),O(),p==null||p(),this},updateContainerElements:function(c){var f=[].concat(c).filter(Boolean);return i.containers=f.map(function(p){return typeof p=="string"?r.querySelector(p):p}),i.active&&v(),O(),this}},s.updateContainerElements(e),s};function kr(o,e={}){let t;const{immediate:r,...n}=e,a=oe(!1),i=oe(!1),s=d=>t&&t.activate(d),u=d=>t&&t.deactivate(d),l=()=>{t&&(t.pause(),i.value=!0)},h=()=>{t&&(t.unpause(),i.value=!1)};return $e(()=>kt(o),d=>{d&&(t=Dr(d,{...n,onActivate(){a.value=!0,e.onActivate&&e.onActivate()},onDeactivate(){a.value=!1,e.onDeactivate&&e.onDeactivate()}}),r&&s())},{flush:"post"}),Ot(()=>u()),{hasFocus:a,isPaused:i,activate:s,deactivate:u,pause:l,unpause:h}}class fe{constructor(e,t=!0,r=[],n=5e3){this.ctx=e,this.iframes=t,this.exclude=r,this.iframesTimeout=n}static matches(e,t){const r=typeof t=="string"?[t]:t,n=e.matches||e.matchesSelector||e.msMatchesSelector||e.mozMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector;if(n){let a=!1;return r.every(i=>n.call(e,i)?(a=!0,!1):!0),a}else return!1}getContexts(){let e,t=[];return typeof this.ctx>"u"||!this.ctx?e=[]:NodeList.prototype.isPrototypeOf(this.ctx)?e=Array.prototype.slice.call(this.ctx):Array.isArray(this.ctx)?e=this.ctx:typeof this.ctx=="string"?e=Array.prototype.slice.call(document.querySelectorAll(this.ctx)):e=[this.ctx],e.forEach(r=>{const n=t.filter(a=>a.contains(r)).length>0;t.indexOf(r)===-1&&!n&&t.push(r)}),t}getIframeContents(e,t,r=()=>{}){let n;try{const a=e.contentWindow;if(n=a.document,!a||!n)throw new Error("iframe inaccessible")}catch{r()}n&&t(n)}isIframeBlank(e){const t="about:blank",r=e.getAttribute("src").trim();return e.contentWindow.location.href===t&&r!==t&&r}observeIframeLoad(e,t,r){let n=!1,a=null;const i=()=>{if(!n){n=!0,clearTimeout(a);try{this.isIframeBlank(e)||(e.removeEventListener("load",i),this.getIframeContents(e,t,r))}catch{r()}}};e.addEventListener("load",i),a=setTimeout(i,this.iframesTimeout)}onIframeReady(e,t,r){try{e.contentWindow.document.readyState==="complete"?this.isIframeBlank(e)?this.observeIframeLoad(e,t,r):this.getIframeContents(e,t,r):this.observeIframeLoad(e,t,r)}catch{r()}}waitForIframes(e,t){let r=0;this.forEachIframe(e,()=>!0,n=>{r++,this.waitForIframes(n.querySelector("html"),()=>{--r||t()})},n=>{n||t()})}forEachIframe(e,t,r,n=()=>{}){let a=e.querySelectorAll("iframe"),i=a.length,s=0;a=Array.prototype.slice.call(a);const u=()=>{--i<=0&&n(s)};i||u(),a.forEach(l=>{fe.matches(l,this.exclude)?u():this.onIframeReady(l,h=>{t(l)&&(s++,r(h)),u()},u)})}createIterator(e,t,r){return document.createNodeIterator(e,t,r,!1)}createInstanceOnIframe(e){return new fe(e.querySelector("html"),this.iframes)}compareNodeIframe(e,t,r){const n=e.compareDocumentPosition(r),a=Node.DOCUMENT_POSITION_PRECEDING;if(n&a)if(t!==null){const i=t.compareDocumentPosition(r),s=Node.DOCUMENT_POSITION_FOLLOWING;if(i&s)return!0}else return!0;return!1}getIteratorNode(e){const t=e.previousNode();let r;return t===null?r=e.nextNode():r=e.nextNode()&&e.nextNode(),{prevNode:t,node:r}}checkIframeFilter(e,t,r,n){let a=!1,i=!1;return n.forEach((s,u)=>{s.val===r&&(a=u,i=s.handled)}),this.compareNodeIframe(e,t,r)?(a===!1&&!i?n.push({val:r,handled:!0}):a!==!1&&!i&&(n[a].handled=!0),!0):(a===!1&&n.push({val:r,handled:!1}),!1)}handleOpenIframes(e,t,r,n){e.forEach(a=>{a.handled||this.getIframeContents(a.val,i=>{this.createInstanceOnIframe(i).forEachNode(t,r,n)})})}iterateThroughNodes(e,t,r,n,a){const i=this.createIterator(t,e,n);let s=[],u=[],l,h,d=()=>({prevNode:h,node:l}=this.getIteratorNode(i),l);for(;d();)this.iframes&&this.forEachIframe(t,v=>this.checkIframeFilter(l,h,v,s),v=>{this.createInstanceOnIframe(v).forEachNode(e,y=>u.push(y),n)}),u.push(l);u.forEach(v=>{r(v)}),this.iframes&&this.handleOpenIframes(s,e,r,n),a()}forEachNode(e,t,r,n=()=>{}){const a=this.getContexts();let i=a.length;i||n(),a.forEach(s=>{const u=()=>{this.iterateThroughNodes(e,s,t,r,()=>{--i<=0&&n()})};this.iframes?this.waitForIframes(s,u):u()})}}let Or=class{constructor(e){this.ctx=e,this.ie=!1;const t=window.navigator.userAgent;(t.indexOf("MSIE")>-1||t.indexOf("Trident")>-1)&&(this.ie=!0)}set opt(e){this._opt=Object.assign({},{element:"",className:"",exclude:[],iframes:!1,iframesTimeout:5e3,separateWordSearch:!0,diacritics:!0,synonyms:{},accuracy:"partially",acrossElements:!1,caseSensitive:!1,ignoreJoiners:!1,ignoreGroups:0,ignorePunctuation:[],wildcards:"disabled",each:()=>{},noMatch:()=>{},filter:()=>!0,done:()=>{},debug:!1,log:window.console},e)}get opt(){return this._opt}get iterator(){return new fe(this.ctx,this.opt.iframes,this.opt.exclude,this.opt.iframesTimeout)}log(e,t="debug"){const r=this.opt.log;this.opt.debug&&typeof r=="object"&&typeof r[t]=="function"&&r[t](`mark.js: ${e}`)}escapeStr(e){return e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")}createRegExp(e){return this.opt.wildcards!=="disabled"&&(e=this.setupWildcardsRegExp(e)),e=this.escapeStr(e),Object.keys(this.opt.synonyms).length&&(e=this.createSynonymsRegExp(e)),(this.opt.ignoreJoiners||this.opt.ignorePunctuation.length)&&(e=this.setupIgnoreJoinersRegExp(e)),this.opt.diacritics&&(e=this.createDiacriticsRegExp(e)),e=this.createMergedBlanksRegExp(e),(this.opt.ignoreJoiners||this.opt.ignorePunctuation.length)&&(e=this.createJoinersRegExp(e)),this.opt.wildcards!=="disabled"&&(e=this.createWildcardsRegExp(e)),e=this.createAccuracyRegExp(e),e}createSynonymsRegExp(e){const t=this.opt.synonyms,r=this.opt.caseSensitive?"":"i",n=this.opt.ignoreJoiners||this.opt.ignorePunctuation.length?"\0":"";for(let a in t)if(t.hasOwnProperty(a)){const i=t[a],s=this.opt.wildcards!=="disabled"?this.setupWildcardsRegExp(a):this.escapeStr(a),u=this.opt.wildcards!=="disabled"?this.setupWildcardsRegExp(i):this.escapeStr(i);s!==""&&u!==""&&(e=e.replace(new RegExp(`(${this.escapeStr(s)}|${this.escapeStr(u)})`,`gm${r}`),n+`(${this.processSynomyms(s)}|${this.processSynomyms(u)})`+n))}return e}processSynomyms(e){return(this.opt.ignoreJoiners||this.opt.ignorePunctuation.length)&&(e=this.setupIgnoreJoinersRegExp(e)),e}setupWildcardsRegExp(e){return e=e.replace(/(?:\\)*\?/g,t=>t.charAt(0)==="\\"?"?":""),e.replace(/(?:\\)*\*/g,t=>t.charAt(0)==="\\"?"*":"")}createWildcardsRegExp(e){let t=this.opt.wildcards==="withSpaces";return e.replace(/\u0001/g,t?"[\\S\\s]?":"\\S?").replace(/\u0002/g,t?"[\\S\\s]*?":"\\S*")}setupIgnoreJoinersRegExp(e){return e.replace(/[^(|)\\]/g,(t,r,n)=>{let a=n.charAt(r+1);return/[(|)\\]/.test(a)||a===""?t:t+"\0"})}createJoinersRegExp(e){let t=[];const r=this.opt.ignorePunctuation;return Array.isArray(r)&&r.length&&t.push(this.escapeStr(r.join(""))),this.opt.ignoreJoiners&&t.push("\\u00ad\\u200b\\u200c\\u200d"),t.length?e.split(/\u0000+/).join(`[${t.join("")}]*`):e}createDiacriticsRegExp(e){const t=this.opt.caseSensitive?"":"i",r=this.opt.caseSensitive?["aàáảãạăằắẳẵặâầấẩẫậäåāą","AÀÁẢÃẠĂẰẮẲẴẶÂẦẤẨẪẬÄÅĀĄ","cçćč","CÇĆČ","dđď","DĐĎ","eèéẻẽẹêềếểễệëěēę","EÈÉẺẼẸÊỀẾỂỄỆËĚĒĘ","iìíỉĩịîïī","IÌÍỈĨỊÎÏĪ","lł","LŁ","nñňń","NÑŇŃ","oòóỏõọôồốổỗộơởỡớờợöøō","OÒÓỎÕỌÔỒỐỔỖỘƠỞỠỚỜỢÖØŌ","rř","RŘ","sšśșş","SŠŚȘŞ","tťțţ","TŤȚŢ","uùúủũụưừứửữựûüůū","UÙÚỦŨỤƯỪỨỬỮỰÛÜŮŪ","yýỳỷỹỵÿ","YÝỲỶỸỴŸ","zžżź","ZŽŻŹ"]:["aàáảãạăằắẳẵặâầấẩẫậäåāąAÀÁẢÃẠĂẰẮẲẴẶÂẦẤẨẪẬÄÅĀĄ","cçćčCÇĆČ","dđďDĐĎ","eèéẻẽẹêềếểễệëěēęEÈÉẺẼẸÊỀẾỂỄỆËĚĒĘ","iìíỉĩịîïīIÌÍỈĨỊÎÏĪ","lłLŁ","nñňńNÑŇŃ","oòóỏõọôồốổỗộơởỡớờợöøōOÒÓỎÕỌÔỒỐỔỖỘƠỞỠỚỜỢÖØŌ","rřRŘ","sšśșşSŠŚȘŞ","tťțţTŤȚŢ","uùúủũụưừứửữựûüůūUÙÚỦŨỤƯỪỨỬỮỰÛÜŮŪ","yýỳỷỹỵÿYÝỲỶỸỴŸ","zžżźZŽŻŹ"];let n=[];return e.split("").forEach(a=>{r.every(i=>{if(i.indexOf(a)!==-1){if(n.indexOf(i)>-1)return!1;e=e.replace(new RegExp(`[${i}]`,`gm${t}`),`[${i}]`),n.push(i)}return!0})}),e}createMergedBlanksRegExp(e){return e.replace(/[\s]+/gmi,"[\\s]+")}createAccuracyRegExp(e){const t="!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~¡¿";let r=this.opt.accuracy,n=typeof r=="string"?r:r.value,a=typeof r=="string"?[]:r.limiters,i="";switch(a.forEach(s=>{i+=`|${this.escapeStr(s)}`}),n){case"partially":default:return`()(${e})`;case"complementary":return i="\\s"+(i||this.escapeStr(t)),`()([^${i}]*${e}[^${i}]*)`;case"exactly":return`(^|\\s${i})(${e})(?=$|\\s${i})`}}getSeparatedKeywords(e){let t=[];return e.forEach(r=>{this.opt.separateWordSearch?r.split(" ").forEach(n=>{n.trim()&&t.indexOf(n)===-1&&t.push(n)}):r.trim()&&t.indexOf(r)===-1&&t.push(r)}),{keywords:t.sort((r,n)=>n.length-r.length),length:t.length}}isNumeric(e){return Number(parseFloat(e))==e}checkRanges(e){if(!Array.isArray(e)||Object.prototype.toString.call(e[0])!=="[object Object]")return this.log("markRanges() will only accept an array of objects"),this.opt.noMatch(e),[];const t=[];let r=0;return e.sort((n,a)=>n.start-a.start).forEach(n=>{let{start:a,end:i,valid:s}=this.callNoMatchOnInvalidRanges(n,r);s&&(n.start=a,n.length=i-a,t.push(n),r=i)}),t}callNoMatchOnInvalidRanges(e,t){let r,n,a=!1;return e&&typeof e.start<"u"?(r=parseInt(e.start,10),n=r+parseInt(e.length,10),this.isNumeric(e.start)&&this.isNumeric(e.length)&&n-t>0&&n-r>0?a=!0:(this.log(`Ignoring invalid or overlapping range: ${JSON.stringify(e)}`),this.opt.noMatch(e))):(this.log(`Ignoring invalid range: ${JSON.stringify(e)}`),this.opt.noMatch(e)),{start:r,end:n,valid:a}}checkWhitespaceRanges(e,t,r){let n,a=!0,i=r.length,s=t-i,u=parseInt(e.start,10)-s;return u=u>i?i:u,n=u+parseInt(e.length,10),n>i&&(n=i,this.log(`End range automatically set to the max value of ${i}`)),u<0||n-u<0||u>i||n>i?(a=!1,this.log(`Invalid range: ${JSON.stringify(e)}`),this.opt.noMatch(e)):r.substring(u,n).replace(/\s+/g,"")===""&&(a=!1,this.log("Skipping whitespace only range: "+JSON.stringify(e)),this.opt.noMatch(e)),{start:u,end:n,valid:a}}getTextNodes(e){let t="",r=[];this.iterator.forEachNode(NodeFilter.SHOW_TEXT,n=>{r.push({start:t.length,end:(t+=n.textContent).length,node:n})},n=>this.matchesExclude(n.parentNode)?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_ACCEPT,()=>{e({value:t,nodes:r})})}matchesExclude(e){return fe.matches(e,this.opt.exclude.concat(["script","style","title","head","html"]))}wrapRangeInTextNode(e,t,r){const n=this.opt.element?this.opt.element:"mark",a=e.splitText(t),i=a.splitText(r-t);let s=document.createElement(n);return s.setAttribute("data-markjs","true"),this.opt.className&&s.setAttribute("class",this.opt.className),s.textContent=a.textContent,a.parentNode.replaceChild(s,a),i}wrapRangeInMappedTextNode(e,t,r,n,a){e.nodes.every((i,s)=>{const u=e.nodes[s+1];if(typeof u>"u"||u.start>t){if(!n(i.node))return!1;const l=t-i.start,h=(r>i.end?i.end:r)-i.start,d=e.value.substr(0,i.start),v=e.value.substr(h+i.start);if(i.node=this.wrapRangeInTextNode(i.node,l,h),e.value=d+v,e.nodes.forEach((y,b)=>{b>=s&&(e.nodes[b].start>0&&b!==s&&(e.nodes[b].start-=h),e.nodes[b].end-=h)}),r-=h,a(i.node.previousSibling,i.start),r>i.end)t=i.end;else return!1}return!0})}wrapMatches(e,t,r,n,a){const i=t===0?0:t+1;this.getTextNodes(s=>{s.nodes.forEach(u=>{u=u.node;let l;for(;(l=e.exec(u.textContent))!==null&&l[i]!=="";){if(!r(l[i],u))continue;let h=l.index;if(i!==0)for(let d=1;d<i;d++)h+=l[d].length;u=this.wrapRangeInTextNode(u,h,h+l[i].length),n(u.previousSibling),e.lastIndex=0}}),a()})}wrapMatchesAcrossElements(e,t,r,n,a){const i=t===0?0:t+1;this.getTextNodes(s=>{let u;for(;(u=e.exec(s.value))!==null&&u[i]!=="";){let l=u.index;if(i!==0)for(let d=1;d<i;d++)l+=u[d].length;const h=l+u[i].length;this.wrapRangeInMappedTextNode(s,l,h,d=>r(u[i],d),(d,v)=>{e.lastIndex=v,n(d)})}a()})}wrapRangeFromIndex(e,t,r,n){this.getTextNodes(a=>{const i=a.value.length;e.forEach((s,u)=>{let{start:l,end:h,valid:d}=this.checkWhitespaceRanges(s,i,a.value);d&&this.wrapRangeInMappedTextNode(a,l,h,v=>t(v,s,a.value.substring(l,h),u),v=>{r(v,s)})}),n()})}unwrapMatches(e){const t=e.parentNode;let r=document.createDocumentFragment();for(;e.firstChild;)r.appendChild(e.removeChild(e.firstChild));t.replaceChild(r,e),this.ie?this.normalizeTextNode(t):t.normalize()}normalizeTextNode(e){if(e){if(e.nodeType===3)for(;e.nextSibling&&e.nextSibling.nodeType===3;)e.nodeValue+=e.nextSibling.nodeValue,e.parentNode.removeChild(e.nextSibling);else this.normalizeTextNode(e.firstChild);this.normalizeTextNode(e.nextSibling)}}markRegExp(e,t){this.opt=t,this.log(`Searching with expression "${e}"`);let r=0,n="wrapMatches";const a=i=>{r++,this.opt.each(i)};this.opt.acrossElements&&(n="wrapMatchesAcrossElements"),this[n](e,this.opt.ignoreGroups,(i,s)=>this.opt.filter(s,i,r),a,()=>{r===0&&this.opt.noMatch(e),this.opt.done(r)})}mark(e,t){this.opt=t;let r=0,n="wrapMatches";const{keywords:a,length:i}=this.getSeparatedKeywords(typeof e=="string"?[e]:e),s=this.opt.caseSensitive?"":"i",u=l=>{let h=new RegExp(this.createRegExp(l),`gm${s}`),d=0;this.log(`Searching with expression "${h}"`),this[n](h,1,(v,y)=>this.opt.filter(y,l,r,d),v=>{d++,r++,this.opt.each(v)},()=>{d===0&&this.opt.noMatch(l),a[i-1]===l?this.opt.done(r):u(a[a.indexOf(l)+1])})};this.opt.acrossElements&&(n="wrapMatchesAcrossElements"),i===0?this.opt.done(r):u(a[0])}markRanges(e,t){this.opt=t;let r=0,n=this.checkRanges(e);n&&n.length?(this.log("Starting to mark with the following ranges: "+JSON.stringify(n)),this.wrapRangeFromIndex(n,(a,i,s,u)=>this.opt.filter(a,i,s,u),(a,i)=>{r++,this.opt.each(a,i)},()=>{this.opt.done(r)})):this.opt.done(r)}unmark(e){this.opt=e;let t=this.opt.element?this.opt.element:"*";t+="[data-markjs]",this.opt.className&&(t+=`.${this.opt.className}`),this.log(`Removal selector "${t}"`),this.iterator.forEachNode(NodeFilter.SHOW_ELEMENT,r=>{this.unwrapMatches(r)},r=>{const n=fe.matches(r,t),a=this.matchesExclude(r);return!n||a?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_ACCEPT},this.opt.done)}};function Rr(o){const e=new Or(o);return this.mark=(t,r)=>(e.mark(t,r),this),this.markRegExp=(t,r)=>(e.markRegExp(t,r),this),this.markRanges=(t,r)=>(e.markRanges(t,r),this),this.unmark=t=>(e.unmark(t),this),this}var W=function(){return W=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++){t=arguments[r];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},W.apply(this,arguments)};function _r(o,e,t,r){function n(a){return a instanceof t?a:new t(function(i){i(a)})}return new(t||(t=Promise))(function(a,i){function s(h){try{l(r.next(h))}catch(d){i(d)}}function u(h){try{l(r.throw(h))}catch(d){i(d)}}function l(h){h.done?a(h.value):n(h.value).then(s,u)}l((r=r.apply(o,[])).next())})}function Mr(o,e){var t={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},r,n,a,i;return i={next:s(0),throw:s(1),return:s(2)},typeof Symbol=="function"&&(i[Symbol.iterator]=function(){return this}),i;function s(l){return function(h){return u([l,h])}}function u(l){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,l[0]&&(t=0)),t;)try{if(r=1,n&&(a=l[0]&2?n.return:l[0]?n.throw||((a=n.return)&&a.call(n),0):n.next)&&!(a=a.call(n,l[1])).done)return a;switch(n=0,a&&(l=[l[0]&2,a.value]),l[0]){case 0:case 1:a=l;break;case 4:return t.label++,{value:l[1],done:!1};case 5:t.label++,n=l[1],l=[0];continue;case 7:l=t.ops.pop(),t.trys.pop();continue;default:if(a=t.trys,!(a=a.length>0&&a[a.length-1])&&(l[0]===6||l[0]===2)){t=0;continue}if(l[0]===3&&(!a||l[1]>a[0]&&l[1]<a[3])){t.label=l[1];break}if(l[0]===6&&t.label<a[1]){t.label=a[1],a=l;break}if(a&&t.label<a[2]){t.label=a[2],t.ops.push(l);break}a[2]&&t.ops.pop(),t.trys.pop();continue}l=e.call(o,t)}catch(h){l=[6,h],n=0}finally{r=a=0}if(l[0]&5)throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}}function D(o){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&o[e],r=0;if(t)return t.call(o);if(o&&typeof o.length=="number")return{next:function(){return o&&r>=o.length&&(o=void 0),{value:o&&o[r++],done:!o}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function J(o,e){var t=typeof Symbol=="function"&&o[Symbol.iterator];if(!t)return o;var r=t.call(o),n,a=[],i;try{for(;(e===void 0||e-- >0)&&!(n=r.next()).done;)a.push(n.value)}catch(s){i={error:s}}finally{try{n&&!n.done&&(t=r.return)&&t.call(r)}finally{if(i)throw i.error}}return a}var Lr="ENTRIES",Ft="KEYS",Et="VALUES",G="",Me=function(){function o(e,t){var r=e._tree,n=Array.from(r.keys());this.set=e,this._type=t,this._path=n.length>0?[{node:r,keys:n}]:[]}return o.prototype.next=function(){var e=this.dive();return this.backtrack(),e},o.prototype.dive=function(){if(this._path.length===0)return{done:!0,value:void 0};var e=ce(this._path),t=e.node,r=e.keys;if(ce(r)===G)return{done:!1,value:this.result()};var n=t.get(ce(r));return this._path.push({node:n,keys:Array.from(n.keys())}),this.dive()},o.prototype.backtrack=function(){if(this._path.length!==0){var e=ce(this._path).keys;e.pop(),!(e.length>0)&&(this._path.pop(),this.backtrack())}},o.prototype.key=function(){return this.set._prefix+this._path.map(function(e){var t=e.keys;return ce(t)}).filter(function(e){return e!==G}).join("")},o.prototype.value=function(){return ce(this._path).node.get(G)},o.prototype.result=function(){switch(this._type){case Et:return this.value();case Ft:return this.key();default:return[this.key(),this.value()]}},o.prototype[Symbol.iterator]=function(){return this},o}(),ce=function(o){return o[o.length-1]},Pr=function(o,e,t){var r=new Map;if(e===void 0)return r;for(var n=e.length+1,a=n+t,i=new Uint8Array(a*n).fill(t+1),s=0;s<n;++s)i[s]=s;for(var u=1;u<a;++u)i[u*n]=u;return St(o,e,t,r,i,1,n,""),r},St=function(o,e,t,r,n,a,i,s){var u,l,h=a*i;try{e:for(var d=D(o.keys()),v=d.next();!v.done;v=d.next()){var y=v.value;if(y===G){var b=n[h-1];b<=t&&r.set(s,[o.get(y),b])}else{for(var E=a,g=0;g<y.length;++g,++E){for(var S=y[g],T=i*E,F=T-i,L=n[T],_=Math.max(0,E-t-1),V=Math.min(i-1,E+t),N=_;N<V;++N){var R=S!==e[N],A=n[F+N]+ +R,O=n[F+N+1]+1,w=n[T+N]+1,c=n[T+N+1]=Math.min(A,O,w);c<L&&(L=c)}if(L>t)continue e}St(o.get(y),e,t,r,n,E,i,s+y)}}}catch(f){u={error:f}}finally{try{v&&!v.done&&(l=d.return)&&l.call(d)}finally{if(u)throw u.error}}},Le=function(){function o(e,t){e===void 0&&(e=new Map),t===void 0&&(t=""),this._size=void 0,this._tree=e,this._prefix=t}return o.prototype.atPrefix=function(e){var t,r;if(!e.startsWith(this._prefix))throw new Error("Mismatched prefix");var n=J(ke(this._tree,e.slice(this._prefix.length)),2),a=n[0],i=n[1];if(a===void 0){var s=J(je(i),2),u=s[0],l=s[1];try{for(var h=D(u.keys()),d=h.next();!d.done;d=h.next()){var v=d.value;if(v!==G&&v.startsWith(l)){var y=new Map;return y.set(v.slice(l.length),u.get(v)),new o(y,e)}}}catch(b){t={error:b}}finally{try{d&&!d.done&&(r=h.return)&&r.call(h)}finally{if(t)throw t.error}}}return new o(a,e)},o.prototype.clear=function(){this._size=void 0,this._tree.clear()},o.prototype.delete=function(e){return this._size=void 0,zr(this._tree,e)},o.prototype.entries=function(){return new Me(this,Lr)},o.prototype.forEach=function(e){var t,r;try{for(var n=D(this),a=n.next();!a.done;a=n.next()){var i=J(a.value,2),s=i[0],u=i[1];e(s,u,this)}}catch(l){t={error:l}}finally{try{a&&!a.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}},o.prototype.fuzzyGet=function(e,t){return Pr(this._tree,e,t)},o.prototype.get=function(e){var t=Ke(this._tree,e);return t!==void 0?t.get(G):void 0},o.prototype.has=function(e){var t=Ke(this._tree,e);return t!==void 0&&t.has(G)},o.prototype.keys=function(){return new Me(this,Ft)},o.prototype.set=function(e,t){if(typeof e!="string")throw new Error("key must be a string");this._size=void 0;var r=Pe(this._tree,e);return r.set(G,t),this},Object.defineProperty(o.prototype,"size",{get:function(){if(this._size)return this._size;this._size=0;for(var e=this.entries();!e.next().done;)this._size+=1;return this._size},enumerable:!1,configurable:!0}),o.prototype.update=function(e,t){if(typeof e!="string")throw new Error("key must be a string");this._size=void 0;var r=Pe(this._tree,e);return r.set(G,t(r.get(G))),this},o.prototype.fetch=function(e,t){if(typeof e!="string")throw new Error("key must be a string");this._size=void 0;var r=Pe(this._tree,e),n=r.get(G);return n===void 0&&r.set(G,n=t()),n},o.prototype.values=function(){return new Me(this,Et)},o.prototype[Symbol.iterator]=function(){return this.entries()},o.from=function(e){var t,r,n=new o;try{for(var a=D(e),i=a.next();!i.done;i=a.next()){var s=J(i.value,2),u=s[0],l=s[1];n.set(u,l)}}catch(h){t={error:h}}finally{try{i&&!i.done&&(r=a.return)&&r.call(a)}finally{if(t)throw t.error}}return n},o.fromObject=function(e){return o.from(Object.entries(e))},o}(),ke=function(o,e,t){var r,n;if(t===void 0&&(t=[]),e.length===0||o==null)return[o,t];try{for(var a=D(o.keys()),i=a.next();!i.done;i=a.next()){var s=i.value;if(s!==G&&e.startsWith(s))return t.push([o,s]),ke(o.get(s),e.slice(s.length),t)}}catch(u){r={error:u}}finally{try{i&&!i.done&&(n=a.return)&&n.call(a)}finally{if(r)throw r.error}}return t.push([o,e]),ke(void 0,"",t)},Ke=function(o,e){var t,r;if(e.length===0||o==null)return o;try{for(var n=D(o.keys()),a=n.next();!a.done;a=n.next()){var i=a.value;if(i!==G&&e.startsWith(i))return Ke(o.get(i),e.slice(i.length))}}catch(s){t={error:s}}finally{try{a&&!a.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}},Pe=function(o,e){var t,r,n=e.length;e:for(var a=0;o&&a<n;){try{for(var i=(t=void 0,D(o.keys())),s=i.next();!s.done;s=i.next()){var u=s.value;if(u!==G&&e[a]===u[0]){for(var l=Math.min(n-a,u.length),h=1;h<l&&e[a+h]===u[h];)++h;var d=o.get(u);if(h===u.length)o=d;else{var v=new Map;v.set(u.slice(h),d),o.set(e.slice(a,a+h),v),o.delete(u),o=v}a+=h;continue e}}}catch(b){t={error:b}}finally{try{s&&!s.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}var y=new Map;return o.set(e.slice(a),y),y}return o},zr=function(o,e){var t=J(ke(o,e),2),r=t[0],n=t[1];if(r!==void 0){if(r.delete(G),r.size===0)At(n);else if(r.size===1){var a=J(r.entries().next().value,2),i=a[0],s=a[1];Tt(n,i,s)}}},At=function(o){if(o.length!==0){var e=J(je(o),2),t=e[0],r=e[1];if(t.delete(r),t.size===0)At(o.slice(0,-1));else if(t.size===1){var n=J(t.entries().next().value,2),a=n[0],i=n[1];a!==G&&Tt(o.slice(0,-1),a,i)}}},Tt=function(o,e,t){if(o.length!==0){var r=J(je(o),2),n=r[0],a=r[1];n.set(a+e,t),n.delete(a)}},je=function(o){return o[o.length-1]},me,Ge="or",Nt="and",Br="and_not",Vr=function(){function o(e){if((e==null?void 0:e.fields)==null)throw new Error('MiniSearch: option "fields" must be provided');var t=e.autoVacuum==null||e.autoVacuum===!0?Ve:e.autoVacuum;this._options=W(W(W({},Be),e),{autoVacuum:t,searchOptions:W(W({},ht),e.searchOptions||{}),autoSuggestOptions:W(W({},Ur),e.autoSuggestOptions||{})}),this._index=new Le,this._documentCount=0,this._documentIds=new Map,this._idToShortId=new Map,this._fieldIds={},this._fieldLength=new Map,this._avgFieldLength=[],this._nextId=0,this._storedFields=new Map,this._dirtCount=0,this._currentVacuum=null,this._enqueuedVacuum=null,this._enqueuedVacuumConditions=Ue,this.addFields(this._options.fields)}return o.prototype.add=function(e){var t,r,n,a,i,s,u=this._options,l=u.extractField,h=u.tokenize,d=u.processTerm,v=u.fields,y=u.idField,b=l(e,y);if(b==null)throw new Error('MiniSearch: document does not have ID field "'.concat(y,'"'));if(this._idToShortId.has(b))throw new Error("MiniSearch: duplicate ID ".concat(b));var E=this.addDocumentId(b);this.saveStoredFields(E,e);try{for(var g=D(v),S=g.next();!S.done;S=g.next()){var T=S.value,F=l(e,T);if(F!=null){var L=h(F.toString(),T),_=this._fieldIds[T],V=new Set(L).size;this.addFieldLength(E,_,this._documentCount-1,V);try{for(var N=(n=void 0,D(L)),R=N.next();!R.done;R=N.next()){var A=R.value,O=d(A,T);if(Array.isArray(O))try{for(var w=(i=void 0,D(O)),c=w.next();!c.done;c=w.next()){var f=c.value;this.addTerm(_,E,f)}}catch(p){i={error:p}}finally{try{c&&!c.done&&(s=w.return)&&s.call(w)}finally{if(i)throw i.error}}else O&&this.addTerm(_,E,O)}}catch(p){n={error:p}}finally{try{R&&!R.done&&(a=N.return)&&a.call(N)}finally{if(n)throw n.error}}}}}catch(p){t={error:p}}finally{try{S&&!S.done&&(r=g.return)&&r.call(g)}finally{if(t)throw t.error}}},o.prototype.addAll=function(e){var t,r;try{for(var n=D(e),a=n.next();!a.done;a=n.next()){var i=a.value;this.add(i)}}catch(s){t={error:s}}finally{try{a&&!a.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}},o.prototype.addAllAsync=function(e,t){var r=this;t===void 0&&(t={});var n=t.chunkSize,a=n===void 0?10:n,i={chunk:[],promise:Promise.resolve()},s=e.reduce(function(h,d,v){var y=h.chunk,b=h.promise;return y.push(d),(v+1)%a===0?{chunk:[],promise:b.then(function(){return new Promise(function(E){return setTimeout(E,0)})}).then(function(){return r.addAll(y)})}:{chunk:y,promise:b}},i),u=s.chunk,l=s.promise;return l.then(function(){return r.addAll(u)})},o.prototype.remove=function(e){var t,r,n,a,i,s,u=this._options,l=u.tokenize,h=u.processTerm,d=u.extractField,v=u.fields,y=u.idField,b=d(e,y);if(b==null)throw new Error('MiniSearch: document does not have ID field "'.concat(y,'"'));var E=this._idToShortId.get(b);if(E==null)throw new Error("MiniSearch: cannot remove document with ID ".concat(b,": it is not in the index"));try{for(var g=D(v),S=g.next();!S.done;S=g.next()){var T=S.value,F=d(e,T);if(F!=null){var L=l(F.toString(),T),_=this._fieldIds[T],V=new Set(L).size;this.removeFieldLength(E,_,this._documentCount,V);try{for(var N=(n=void 0,D(L)),R=N.next();!R.done;R=N.next()){var A=R.value,O=h(A,T);if(Array.isArray(O))try{for(var w=(i=void 0,D(O)),c=w.next();!c.done;c=w.next()){var f=c.value;this.removeTerm(_,E,f)}}catch(p){i={error:p}}finally{try{c&&!c.done&&(s=w.return)&&s.call(w)}finally{if(i)throw i.error}}else O&&this.removeTerm(_,E,O)}}catch(p){n={error:p}}finally{try{R&&!R.done&&(a=N.return)&&a.call(N)}finally{if(n)throw n.error}}}}}catch(p){t={error:p}}finally{try{S&&!S.done&&(r=g.return)&&r.call(g)}finally{if(t)throw t.error}}this._storedFields.delete(E),this._documentIds.delete(E),this._idToShortId.delete(b),this._fieldLength.delete(E),this._documentCount-=1},o.prototype.removeAll=function(e){var t,r;if(e)try{for(var n=D(e),a=n.next();!a.done;a=n.next()){var i=a.value;this.remove(i)}}catch(s){t={error:s}}finally{try{a&&!a.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}else{if(arguments.length>0)throw new Error("Expected documents to be present. Omit the argument to remove all documents.");this._index=new Le,this._documentCount=0,this._documentIds=new Map,this._idToShortId=new Map,this._fieldLength=new Map,this._avgFieldLength=[],this._storedFields=new Map,this._nextId=0}},o.prototype.discard=function(e){var t=this,r=this._idToShortId.get(e);if(r==null)throw new Error("MiniSearch: cannot discard document with ID ".concat(e,": it is not in the index"));this._idToShortId.delete(e),this._documentIds.delete(r),this._storedFields.delete(r),(this._fieldLength.get(r)||[]).forEach(function(n,a){t.removeFieldLength(r,a,t._documentCount,n)}),this._fieldLength.delete(r),this._documentCount-=1,this._dirtCount+=1,this.maybeAutoVacuum()},o.prototype.maybeAutoVacuum=function(){if(this._options.autoVacuum!==!1){var e=this._options.autoVacuum,t=e.minDirtFactor,r=e.minDirtCount,n=e.batchSize,a=e.batchWait;this.conditionalVacuum({batchSize:n,batchWait:a},{minDirtCount:r,minDirtFactor:t})}},o.prototype.discardAll=function(e){var t,r,n=this._options.autoVacuum;try{this._options.autoVacuum=!1;try{for(var a=D(e),i=a.next();!i.done;i=a.next()){var s=i.value;this.discard(s)}}catch(u){t={error:u}}finally{try{i&&!i.done&&(r=a.return)&&r.call(a)}finally{if(t)throw t.error}}}finally{this._options.autoVacuum=n}this.maybeAutoVacuum()},o.prototype.replace=function(e){var t=this._options,r=t.idField,n=t.extractField,a=n(e,r);this.discard(a),this.add(e)},o.prototype.vacuum=function(e){return e===void 0&&(e={}),this.conditionalVacuum(e)},o.prototype.conditionalVacuum=function(e,t){var r=this;return this._currentVacuum?(this._enqueuedVacuumConditions=this._enqueuedVacuumConditions&&t,this._enqueuedVacuum!=null?this._enqueuedVacuum:(this._enqueuedVacuum=this._currentVacuum.then(function(){var n=r._enqueuedVacuumConditions;return r._enqueuedVacuumConditions=Ue,r.performVacuuming(e,n)}),this._enqueuedVacuum)):this.vacuumConditionsMet(t)===!1?Promise.resolve():(this._currentVacuum=this.performVacuuming(e),this._currentVacuum)},o.prototype.performVacuuming=function(e,t){return _r(this,void 0,void 0,function(){var r,n,a,i,s,u,l,h,d,v,y,b,E,g,S,T,F,L,_,V,N,R,A,O,w;return Mr(this,function(c){switch(c.label){case 0:if(r=this._dirtCount,!this.vacuumConditionsMet(t))return[3,10];n=e.batchSize||Je.batchSize,a=e.batchWait||Je.batchWait,i=1,c.label=1;case 1:c.trys.push([1,7,8,9]),s=D(this._index),u=s.next(),c.label=2;case 2:if(u.done)return[3,6];l=J(u.value,2),h=l[0],d=l[1];try{for(v=(R=void 0,D(d)),y=v.next();!y.done;y=v.next()){b=J(y.value,2),E=b[0],g=b[1];try{for(S=(O=void 0,D(g)),T=S.next();!T.done;T=S.next())F=J(T.value,1),L=F[0],!this._documentIds.has(L)&&(g.size<=1?d.delete(E):g.delete(L))}catch(f){O={error:f}}finally{try{T&&!T.done&&(w=S.return)&&w.call(S)}finally{if(O)throw O.error}}}}catch(f){R={error:f}}finally{try{y&&!y.done&&(A=v.return)&&A.call(v)}finally{if(R)throw R.error}}return this._index.get(h).size===0&&this._index.delete(h),i%n!==0?[3,4]:[4,new Promise(function(f){return setTimeout(f,a)})];case 3:c.sent(),c.label=4;case 4:i+=1,c.label=5;case 5:return u=s.next(),[3,2];case 6:return[3,9];case 7:return _=c.sent(),V={error:_},[3,9];case 8:try{u&&!u.done&&(N=s.return)&&N.call(s)}finally{if(V)throw V.error}return[7];case 9:this._dirtCount-=r,c.label=10;case 10:return[4,null];case 11:return c.sent(),this._currentVacuum=this._enqueuedVacuum,this._enqueuedVacuum=null,[2]}})})},o.prototype.vacuumConditionsMet=function(e){if(e==null)return!0;var t=e.minDirtCount,r=e.minDirtFactor;return t=t||Ve.minDirtCount,r=r||Ve.minDirtFactor,this.dirtCount>=t&&this.dirtFactor>=r},Object.defineProperty(o.prototype,"isVacuuming",{get:function(){return this._currentVacuum!=null},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"dirtCount",{get:function(){return this._dirtCount},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"dirtFactor",{get:function(){return this._dirtCount/(1+this._documentCount+this._dirtCount)},enumerable:!1,configurable:!0}),o.prototype.has=function(e){return this._idToShortId.has(e)},o.prototype.getStoredFields=function(e){var t=this._idToShortId.get(e);if(t!=null)return this._storedFields.get(t)},o.prototype.search=function(e,t){var r,n;t===void 0&&(t={});var a=this.executeQuery(e,t),i=[];try{for(var s=D(a),u=s.next();!u.done;u=s.next()){var l=J(u.value,2),h=l[0],d=l[1],v=d.score,y=d.terms,b=d.match,E=y.length||1,g={id:this._documentIds.get(h),score:v*E,terms:Object.keys(b),queryTerms:y,match:b};Object.assign(g,this._storedFields.get(h)),(t.filter==null||t.filter(g))&&i.push(g)}}catch(S){r={error:S}}finally{try{u&&!u.done&&(n=s.return)&&n.call(s)}finally{if(r)throw r.error}}return e===o.wildcard&&t.boostDocument==null&&this._options.searchOptions.boostDocument==null||i.sort(vt),i},o.prototype.autoSuggest=function(e,t){var r,n,a,i;t===void 0&&(t={}),t=W(W({},this._options.autoSuggestOptions),t);var s=new Map;try{for(var u=D(this.search(e,t)),l=u.next();!l.done;l=u.next()){var h=l.value,d=h.score,v=h.terms,y=v.join(" "),b=s.get(y);b!=null?(b.score+=d,b.count+=1):s.set(y,{score:d,terms:v,count:1})}}catch(_){r={error:_}}finally{try{l&&!l.done&&(n=u.return)&&n.call(u)}finally{if(r)throw r.error}}var E=[];try{for(var g=D(s),S=g.next();!S.done;S=g.next()){var T=J(S.value,2),b=T[0],F=T[1],d=F.score,v=F.terms,L=F.count;E.push({suggestion:b,terms:v,score:d/L})}}catch(_){a={error:_}}finally{try{S&&!S.done&&(i=g.return)&&i.call(g)}finally{if(a)throw a.error}}return E.sort(vt),E},Object.defineProperty(o.prototype,"documentCount",{get:function(){return this._documentCount},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"termCount",{get:function(){return this._index.size},enumerable:!1,configurable:!0}),o.loadJSON=function(e,t){if(t==null)throw new Error("MiniSearch: loadJSON should be given the same options used when serializing the index");return this.loadJS(JSON.parse(e),t)},o.getDefault=function(e){if(Be.hasOwnProperty(e))return ze(Be,e);throw new Error('MiniSearch: unknown option "'.concat(e,'"'))},o.loadJS=function(e,t){var r,n,a,i,s,u,l=e.index,h=e.documentCount,d=e.nextId,v=e.documentIds,y=e.fieldIds,b=e.fieldLength,E=e.averageFieldLength,g=e.storedFields,S=e.dirtCount,T=e.serializationVersion;if(T!==1&&T!==2)throw new Error("MiniSearch: cannot deserialize an index created with an incompatible version");var F=new o(t);F._documentCount=h,F._nextId=d,F._documentIds=Te(v),F._idToShortId=new Map,F._fieldIds=y,F._fieldLength=Te(b),F._avgFieldLength=E,F._storedFields=Te(g),F._dirtCount=S||0,F._index=new Le;try{for(var L=D(F._documentIds),_=L.next();!_.done;_=L.next()){var V=J(_.value,2),N=V[0],R=V[1];F._idToShortId.set(R,N)}}catch(z){r={error:z}}finally{try{_&&!_.done&&(n=L.return)&&n.call(L)}finally{if(r)throw r.error}}try{for(var A=D(l),O=A.next();!O.done;O=A.next()){var w=J(O.value,2),c=w[0],f=w[1],p=new Map;try{for(var C=(s=void 0,D(Object.keys(f))),I=C.next();!I.done;I=C.next()){var M=I.value,P=f[M];T===1&&(P=P.ds),p.set(parseInt(M,10),Te(P))}}catch(z){s={error:z}}finally{try{I&&!I.done&&(u=C.return)&&u.call(C)}finally{if(s)throw s.error}}F._index.set(c,p)}}catch(z){a={error:z}}finally{try{O&&!O.done&&(i=A.return)&&i.call(A)}finally{if(a)throw a.error}}return F},o.prototype.executeQuery=function(e,t){var r=this;if(t===void 0&&(t={}),e===o.wildcard)return this.executeWildcardQuery(t);if(typeof e!="string"){var n=W(W(W({},t),e),{queries:void 0}),a=e.queries.map(function(g){return r.executeQuery(g,n)});return this.combineResults(a,n.combineWith)}var i=this._options,s=i.tokenize,u=i.processTerm,l=i.searchOptions,h=W(W({tokenize:s,processTerm:u},l),t),d=h.tokenize,v=h.processTerm,y=d(e).flatMap(function(g){return v(g)}).filter(function(g){return!!g}),b=y.map(Jr(h)),E=b.map(function(g){return r.executeQuerySpec(g,h)});return this.combineResults(E,h.combineWith)},o.prototype.executeQuerySpec=function(e,t){var r,n,a,i,s=W(W({},this._options.searchOptions),t),u=(s.fields||this._options.fields).reduce(function(M,P){var z;return W(W({},M),(z={},z[P]=ze(s.boost,P)||1,z))},{}),l=s.boostDocument,h=s.weights,d=s.maxFuzzy,v=s.bm25,y=W(W({},ht.weights),h),b=y.fuzzy,E=y.prefix,g=this._index.get(e.term),S=this.termResults(e.term,e.term,1,g,u,l,v),T,F;if(e.prefix&&(T=this._index.atPrefix(e.term)),e.fuzzy){var L=e.fuzzy===!0?.2:e.fuzzy,_=L<1?Math.min(d,Math.round(e.term.length*L)):L;_&&(F=this._index.fuzzyGet(e.term,_))}if(T)try{for(var V=D(T),N=V.next();!N.done;N=V.next()){var R=J(N.value,2),A=R[0],O=R[1],w=A.length-e.term.length;if(w){F==null||F.delete(A);var c=E*A.length/(A.length+.3*w);this.termResults(e.term,A,c,O,u,l,v,S)}}}catch(M){r={error:M}}finally{try{N&&!N.done&&(n=V.return)&&n.call(V)}finally{if(r)throw r.error}}if(F)try{for(var f=D(F.keys()),p=f.next();!p.done;p=f.next()){var A=p.value,C=J(F.get(A),2),I=C[0],w=C[1];if(w){var c=b*A.length/(A.length+w);this.termResults(e.term,A,c,I,u,l,v,S)}}}catch(M){a={error:M}}finally{try{p&&!p.done&&(i=f.return)&&i.call(f)}finally{if(a)throw a.error}}return S},o.prototype.executeWildcardQuery=function(e){var t,r,n=new Map,a=W(W({},this._options.searchOptions),e);try{for(var i=D(this._documentIds),s=i.next();!s.done;s=i.next()){var u=J(s.value,2),l=u[0],h=u[1],d=a.boostDocument?a.boostDocument(h,"",this._storedFields.get(l)):1;n.set(l,{score:d,terms:[],match:{}})}}catch(v){t={error:v}}finally{try{s&&!s.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}return n},o.prototype.combineResults=function(e,t){if(t===void 0&&(t=Ge),e.length===0)return new Map;var r=t.toLowerCase();return e.reduce($r[r])||new Map},o.prototype.toJSON=function(){var e,t,r,n,a=[];try{for(var i=D(this._index),s=i.next();!s.done;s=i.next()){var u=J(s.value,2),l=u[0],h=u[1],d={};try{for(var v=(r=void 0,D(h)),y=v.next();!y.done;y=v.next()){var b=J(y.value,2),E=b[0],g=b[1];d[E]=Object.fromEntries(g)}}catch(S){r={error:S}}finally{try{y&&!y.done&&(n=v.return)&&n.call(v)}finally{if(r)throw r.error}}a.push([l,d])}}catch(S){e={error:S}}finally{try{s&&!s.done&&(t=i.return)&&t.call(i)}finally{if(e)throw e.error}}return{documentCount:this._documentCount,nextId:this._nextId,documentIds:Object.fromEntries(this._documentIds),fieldIds:this._fieldIds,fieldLength:Object.fromEntries(this._fieldLength),averageFieldLength:this._avgFieldLength,storedFields:Object.fromEntries(this._storedFields),dirtCount:this._dirtCount,index:a,serializationVersion:2}},o.prototype.termResults=function(e,t,r,n,a,i,s,u){var l,h,d,v,y;if(u===void 0&&(u=new Map),n==null)return u;try{for(var b=D(Object.keys(a)),E=b.next();!E.done;E=b.next()){var g=E.value,S=a[g],T=this._fieldIds[g],F=n.get(T);if(F!=null){var L=F.size,_=this._avgFieldLength[T];try{for(var V=(d=void 0,D(F.keys())),N=V.next();!N.done;N=V.next()){var R=N.value;if(!this._documentIds.has(R)){this.removeTerm(T,R,t),L-=1;continue}var A=i?i(this._documentIds.get(R),t,this._storedFields.get(R)):1;if(A){var O=F.get(R),w=this._fieldLength.get(R)[T],c=Kr(O,L,this._documentCount,w,_,s),f=r*S*A*c,p=u.get(R);if(p){p.score+=f,jr(p.terms,e);var C=ze(p.match,t);C?C.push(g):p.match[t]=[g]}else u.set(R,{score:f,terms:[e],match:(y={},y[t]=[g],y)})}}}catch(I){d={error:I}}finally{try{N&&!N.done&&(v=V.return)&&v.call(V)}finally{if(d)throw d.error}}}}}catch(I){l={error:I}}finally{try{E&&!E.done&&(h=b.return)&&h.call(b)}finally{if(l)throw l.error}}return u},o.prototype.addTerm=function(e,t,r){var n=this._index.fetch(r,pt),a=n.get(e);if(a==null)a=new Map,a.set(t,1),n.set(e,a);else{var i=a.get(t);a.set(t,(i||0)+1)}},o.prototype.removeTerm=function(e,t,r){if(!this._index.has(r)){this.warnDocumentChanged(t,e,r);return}var n=this._index.fetch(r,pt),a=n.get(e);a==null||a.get(t)==null?this.warnDocumentChanged(t,e,r):a.get(t)<=1?a.size<=1?n.delete(e):a.delete(t):a.set(t,a.get(t)-1),this._index.get(r).size===0&&this._index.delete(r)},o.prototype.warnDocumentChanged=function(e,t,r){var n,a;try{for(var i=D(Object.keys(this._fieldIds)),s=i.next();!s.done;s=i.next()){var u=s.value;if(this._fieldIds[u]===t){this._options.logger("warn","MiniSearch: document with ID ".concat(this._documentIds.get(e),' has changed before removal: term "').concat(r,'" was not present in field "').concat(u,'". Removing a document after it has changed can corrupt the index!'),"version_conflict");return}}}catch(l){n={error:l}}finally{try{s&&!s.done&&(a=i.return)&&a.call(i)}finally{if(n)throw n.error}}},o.prototype.addDocumentId=function(e){var t=this._nextId;return this._idToShortId.set(e,t),this._documentIds.set(t,e),this._documentCount+=1,this._nextId+=1,t},o.prototype.addFields=function(e){for(var t=0;t<e.length;t++)this._fieldIds[e[t]]=t},o.prototype.addFieldLength=function(e,t,r,n){var a=this._fieldLength.get(e);a==null&&this._fieldLength.set(e,a=[]),a[t]=n;var i=this._avgFieldLength[t]||0,s=i*r+n;this._avgFieldLength[t]=s/(r+1)},o.prototype.removeFieldLength=function(e,t,r,n){if(r===1){this._avgFieldLength[t]=0;return}var a=this._avgFieldLength[t]*r-n;this._avgFieldLength[t]=a/(r-1)},o.prototype.saveStoredFields=function(e,t){var r,n,a=this._options,i=a.storeFields,s=a.extractField;if(!(i==null||i.length===0)){var u=this._storedFields.get(e);u==null&&this._storedFields.set(e,u={});try{for(var l=D(i),h=l.next();!h.done;h=l.next()){var d=h.value,v=s(t,d);v!==void 0&&(u[d]=v)}}catch(y){r={error:y}}finally{try{h&&!h.done&&(n=l.return)&&n.call(l)}finally{if(r)throw r.error}}}},o.wildcard=Symbol("*"),o}(),ze=function(o,e){return Object.prototype.hasOwnProperty.call(o,e)?o[e]:void 0},$r=(me={},me[Ge]=function(o,e){var t,r;try{for(var n=D(e.keys()),a=n.next();!a.done;a=n.next()){var i=a.value,s=o.get(i);if(s==null)o.set(i,e.get(i));else{var u=e.get(i),l=u.score,h=u.terms,d=u.match;s.score=s.score+l,s.match=Object.assign(s.match,d),dt(s.terms,h)}}}catch(v){t={error:v}}finally{try{a&&!a.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}return o},me[Nt]=function(o,e){var t,r,n=new Map;try{for(var a=D(e.keys()),i=a.next();!i.done;i=a.next()){var s=i.value,u=o.get(s);if(u!=null){var l=e.get(s),h=l.score,d=l.terms,v=l.match;dt(u.terms,d),n.set(s,{score:u.score+h,terms:u.terms,match:Object.assign(u.match,v)})}}}catch(y){t={error:y}}finally{try{i&&!i.done&&(r=a.return)&&r.call(a)}finally{if(t)throw t.error}}return n},me[Br]=function(o,e){var t,r;try{for(var n=D(e.keys()),a=n.next();!a.done;a=n.next()){var i=a.value;o.delete(i)}}catch(s){t={error:s}}finally{try{a&&!a.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}return o},me),Wr={k:1.2,b:.7,d:.5},Kr=function(o,e,t,r,n,a){var i=a.k,s=a.b,u=a.d,l=Math.log(1+(t-e+.5)/(e+.5));return l*(u+o*(i+1)/(o+i*(1-s+s*r/n)))},Jr=function(o){return function(e,t,r){var n=typeof o.fuzzy=="function"?o.fuzzy(e,t,r):o.fuzzy||!1,a=typeof o.prefix=="function"?o.prefix(e,t,r):o.prefix===!0;return{term:e,fuzzy:n,prefix:a}}},Be={idField:"id",extractField:function(o,e){return o[e]},tokenize:function(o){return o.split(Gr)},processTerm:function(o){return o.toLowerCase()},fields:void 0,searchOptions:void 0,storeFields:[],logger:function(o,e){typeof(console==null?void 0:console[o])=="function"&&console[o](e)},autoVacuum:!0},ht={combineWith:Ge,prefix:!1,fuzzy:!1,maxFuzzy:6,boost:{},weights:{fuzzy:.45,prefix:.375},bm25:Wr},Ur={combineWith:Nt,prefix:function(o,e,t){return e===t.length-1}},Je={batchSize:1e3,batchWait:10},Ue={minDirtFactor:.1,minDirtCount:20},Ve=W(W({},Je),Ue),jr=function(o,e){o.includes(e)||o.push(e)},dt=function(o,e){var t,r;try{for(var n=D(e),a=n.next();!a.done;a=n.next()){var i=a.value;o.includes(i)||o.push(i)}}catch(s){t={error:s}}finally{try{a&&!a.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}},vt=function(o,e){var t=o.score,r=e.score;return r-t},pt=function(){return new Map},Te=function(o){var e,t,r=new Map;try{for(var n=D(Object.keys(o)),a=n.next();!a.done;a=n.next()){var i=a.value;r.set(parseInt(i,10),o[i])}}catch(s){e={error:s}}finally{try{a&&!a.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}return r},Gr=/[\n\r -#%-*,-/:;?@[-\]_{}\u00A0\u00A1\u00A7\u00AB\u00B6\u00B7\u00BB\u00BF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061E\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u1680\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2000-\u200A\u2010-\u2029\u202F-\u2043\u2045-\u2051\u2053-\u205F\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u3000-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]+/u;class Qr{constructor(e=10){Oe(this,"max");Oe(this,"cache");this.max=e,this.cache=new Map}get(e){let t=this.cache.get(e);return t!==void 0&&(this.cache.delete(e),this.cache.set(e,t)),t}set(e,t){this.cache.has(e)?this.cache.delete(e):this.cache.size===this.max&&this.cache.delete(this.first()),this.cache.set(e,t)}first(){return this.cache.keys().next().value}clear(){this.cache.clear()}}const Y=o=>(qt("data-v-cb4e1afe"),o=o(),Ht(),o),qr=["aria-owns"],Hr={class:"shell"},Yr=["title"],Zr=Y(()=>k("span",{"aria-hidden":"true",class:"vpi-search search-icon local-search-icon"},null,-1)),Xr=[Zr],ea={class:"search-actions before"},ta=["title"],ra=Y(()=>k("span",{class:"vpi-arrow-left local-search-icon"},null,-1)),aa=[ra],na=["placeholder"],ia={class:"search-actions"},oa=["title"],sa=Y(()=>k("span",{class:"vpi-layout-list local-search-icon"},null,-1)),ua=[sa],la=["disabled","title"],ca=Y(()=>k("span",{class:"vpi-delete local-search-icon"},null,-1)),fa=[ca],ha=["id","role","aria-labelledby"],da=["aria-selected"],va=["href","aria-label","onMouseenter","onFocusin"],pa={class:"titles"},ya=Y(()=>k("span",{class:"title-icon"},"#",-1)),ma=["innerHTML"],ga=Y(()=>k("span",{class:"vpi-chevron-right local-search-icon"},null,-1)),ba={class:"title main"},wa=["innerHTML"],xa={key:0,class:"excerpt-wrapper"},Fa={key:0,class:"excerpt",inert:""},Ea=["innerHTML"],Sa=Y(()=>k("div",{class:"excerpt-gradient-bottom"},null,-1)),Aa=Y(()=>k("div",{class:"excerpt-gradient-top"},null,-1)),Ta={key:0,class:"no-results"},Na={class:"search-keyboard-shortcuts"},Ca=["aria-label"],Ia=Y(()=>k("span",{class:"vpi-arrow-up navigate-icon"},null,-1)),Da=[Ia],ka=["aria-label"],Oa=Y(()=>k("span",{class:"vpi-arrow-down navigate-icon"},null,-1)),Ra=[Oa],_a=["aria-label"],Ma=Y(()=>k("span",{class:"vpi-corner-down-left navigate-icon"},null,-1)),La=[Ma],Pa=["aria-label"],za=Rt({__name:"VPLocalSearchBox",emits:["close"],setup(o,{emit:e}){var P,z;const t=e,r=xe(),n=xe(),a=xe(nr),i=rr(),{activate:s}=kr(r,{immediate:!0,allowOutsideClick:!0,clickOutsideDeactivates:!0,escapeDeactivates:!0}),{localeIndex:u,theme:l}=i,h=tt(async()=>{var m,x,$,K,Q,q,B,U,Z;return it(Vr.loadJSON(($=await((x=(m=a.value)[u.value])==null?void 0:x.call(m)))==null?void 0:$.default,{fields:["title","titles","text"],storeFields:["title","titles"],searchOptions:{fuzzy:.2,prefix:!0,boost:{title:4,text:2,titles:1},...((K=l.value.search)==null?void 0:K.provider)==="local"&&((q=(Q=l.value.search.options)==null?void 0:Q.miniSearch)==null?void 0:q.searchOptions)},...((B=l.value.search)==null?void 0:B.provider)==="local"&&((Z=(U=l.value.search.options)==null?void 0:U.miniSearch)==null?void 0:Z.options)}))}),v=Fe(()=>{var m,x;return((m=l.value.search)==null?void 0:m.provider)==="local"&&((x=l.value.search.options)==null?void 0:x.disableQueryPersistence)===!0}).value?oe(""):_t("vitepress:local-search-filter",""),y=Mt("vitepress:local-search-detailed-list",((P=l.value.search)==null?void 0:P.provider)==="local"&&((z=l.value.search.options)==null?void 0:z.detailedView)===!0),b=Fe(()=>{var m,x,$;return((m=l.value.search)==null?void 0:m.provider)==="local"&&(((x=l.value.search.options)==null?void 0:x.disableDetailedView)===!0||(($=l.value.search.options)==null?void 0:$.detailedView)===!1)}),E=Fe(()=>{var x,$,K,Q,q,B,U;const m=((x=l.value.search)==null?void 0:x.options)??l.value.algolia;return((q=(Q=(K=($=m==null?void 0:m.locales)==null?void 0:$[u.value])==null?void 0:K.translations)==null?void 0:Q.button)==null?void 0:q.buttonText)||((U=(B=m==null?void 0:m.translations)==null?void 0:B.button)==null?void 0:U.buttonText)||"Search"});Lt(()=>{b.value&&(y.value=!1)});const g=xe([]),S=oe(!1);$e(v,()=>{S.value=!1});const T=tt(async()=>{if(n.value)return it(new Rr(n.value))},null),F=new Qr(16);Pt(()=>[h.value,v.value,y.value],async([m,x,$],K,Q)=>{var be,Qe,qe,He;(K==null?void 0:K[0])!==m&&F.clear();let q=!1;if(Q(()=>{q=!0}),!m)return;g.value=m.search(x).slice(0,16),S.value=!0;const B=$?await Promise.all(g.value.map(H=>L(H.id))):[];if(q)return;for(const{id:H,mod:ae}of B){const ne=H.slice(0,H.indexOf("#"));let te=F.get(ne);if(te)continue;te=new Map,F.set(ne,te);const X=ae.default??ae;if(X!=null&&X.render||X!=null&&X.setup){const ie=Yt(X);ie.config.warnHandler=()=>{},ie.provide(Zt,i),Object.defineProperties(ie.config.globalProperties,{$frontmatter:{get(){return i.frontmatter.value}},$params:{get(){return i.page.value.params}}});const Ye=document.createElement("div");ie.mount(Ye),Ye.querySelectorAll("h1, h2, h3, h4, h5, h6").forEach(he=>{var et;const we=(et=he.querySelector("a"))==null?void 0:et.getAttribute("href"),Ze=(we==null?void 0:we.startsWith("#"))&&we.slice(1);if(!Ze)return;let Xe="";for(;(he=he.nextElementSibling)&&!/^h[1-6]$/i.test(he.tagName);)Xe+=he.outerHTML;te.set(Ze,Xe)}),ie.unmount()}if(q)return}const U=new Set;if(g.value=g.value.map(H=>{const[ae,ne]=H.id.split("#"),te=F.get(ae),X=(te==null?void 0:te.get(ne))??"";for(const ie in H.match)U.add(ie);return{...H,text:X}}),await de(),q)return;await new Promise(H=>{var ae;(ae=T.value)==null||ae.unmark({done:()=>{var ne;(ne=T.value)==null||ne.markRegExp(M(U),{done:H})}})});const Z=((be=r.value)==null?void 0:be.querySelectorAll(".result .excerpt"))??[];for(const H of Z)(Qe=H.querySelector('mark[data-markjs="true"]'))==null||Qe.scrollIntoView({block:"center"});(He=(qe=n.value)==null?void 0:qe.firstElementChild)==null||He.scrollIntoView({block:"start"})},{debounce:200,immediate:!0});async function L(m){const x=Xt(m.slice(0,m.indexOf("#")));try{if(!x)throw new Error(`Cannot find file for id: ${m}`);return{id:m,mod:await import(x)}}catch($){return console.error($),{id:m,mod:{}}}}const _=oe(),V=Fe(()=>{var m;return((m=v.value)==null?void 0:m.length)<=0});function N(m=!0){var x,$;(x=_.value)==null||x.focus(),m&&(($=_.value)==null||$.select())}Re(()=>{N()});function R(m){m.pointerType==="mouse"&&N()}const A=oe(-1),O=oe(!1);$e(g,m=>{A.value=m.length?0:-1,w()});function w(){de(()=>{const m=document.querySelector(".result.selected");m==null||m.scrollIntoView({block:"nearest"})})}Ee("ArrowUp",m=>{m.preventDefault(),A.value--,A.value<0&&(A.value=g.value.length-1),O.value=!0,w()}),Ee("ArrowDown",m=>{m.preventDefault(),A.value++,A.value>=g.value.length&&(A.value=0),O.value=!0,w()});const c=zt();Ee("Enter",m=>{if(m.isComposing||m.target instanceof HTMLButtonElement&&m.target.type!=="submit")return;const x=g.value[A.value];if(m.target instanceof HTMLInputElement&&!x){m.preventDefault();return}x&&(c.go(x.id),t("close"))}),Ee("Escape",()=>{t("close")});const p=ar({modal:{displayDetails:"Display detailed list",resetButtonTitle:"Reset search",backButtonTitle:"Close search",noResultsText:"No results for",footer:{selectText:"to select",selectKeyAriaLabel:"enter",navigateText:"to navigate",navigateUpKeyAriaLabel:"up arrow",navigateDownKeyAriaLabel:"down arrow",closeText:"to close",closeKeyAriaLabel:"escape"}}});Re(()=>{window.history.pushState(null,"",null)}),Bt("popstate",m=>{m.preventDefault(),t("close")});const C=Vt($t?document.body:null);Re(()=>{de(()=>{C.value=!0,de().then(()=>s())})}),Wt(()=>{C.value=!1});function I(){v.value="",de().then(()=>N(!1))}function M(m){return new RegExp([...m].sort((x,$)=>$.length-x.length).map(x=>`(${er(x)})`).join("|"),"gi")}return(m,x)=>{var $,K,Q,q;return ee(),Kt(Qt,{to:"body"},[k("div",{ref_key:"el",ref:r,role:"button","aria-owns":($=g.value)!=null&&$.length?"localsearch-list":void 0,"aria-expanded":"true","aria-haspopup":"listbox","aria-labelledby":"localsearch-label",class:"VPLocalSearchBox"},[k("div",{class:"backdrop",onClick:x[0]||(x[0]=B=>m.$emit("close"))}),k("div",Hr,[k("form",{class:"search-bar",onPointerup:x[4]||(x[4]=B=>R(B)),onSubmit:x[5]||(x[5]=Jt(()=>{},["prevent"]))},[k("label",{title:E.value,id:"localsearch-label",for:"localsearch-input"},Xr,8,Yr),k("div",ea,[k("button",{class:"back-button",title:j(p)("modal.backButtonTitle"),onClick:x[1]||(x[1]=B=>m.$emit("close"))},aa,8,ta)]),Ut(k("input",{ref_key:"searchInput",ref:_,"onUpdate:modelValue":x[2]||(x[2]=B=>Gt(v)?v.value=B:null),placeholder:E.value,id:"localsearch-input","aria-labelledby":"localsearch-label",class:"search-input"},null,8,na),[[jt,j(v)]]),k("div",ia,[b.value?Se("",!0):(ee(),re("button",{key:0,class:rt(["toggle-layout-button",{"detailed-list":j(y)}]),type:"button",title:j(p)("modal.displayDetails"),onClick:x[3]||(x[3]=B=>A.value>-1&&(y.value=!j(y)))},ua,10,oa)),k("button",{class:"clear-button",type:"reset",disabled:V.value,title:j(p)("modal.resetButtonTitle"),onClick:I},fa,8,la)])],32),k("ul",{ref_key:"resultsEl",ref:n,id:(K=g.value)!=null&&K.length?"localsearch-list":void 0,role:(Q=g.value)!=null&&Q.length?"listbox":void 0,"aria-labelledby":(q=g.value)!=null&&q.length?"localsearch-label":void 0,class:"results",onMousemove:x[7]||(x[7]=B=>O.value=!1)},[(ee(!0),re(nt,null,at(g.value,(B,U)=>(ee(),re("li",{key:B.id,role:"option","aria-selected":A.value===U?"true":"false"},[k("a",{href:B.id,class:rt(["result",{selected:A.value===U}]),"aria-label":[...B.titles,B.title].join(" > "),onMouseenter:Z=>!O.value&&(A.value=U),onFocusin:Z=>A.value=U,onClick:x[6]||(x[6]=Z=>m.$emit("close"))},[k("div",null,[k("div",pa,[ya,(ee(!0),re(nt,null,at(B.titles,(Z,be)=>(ee(),re("span",{key:be,class:"title"},[k("span",{class:"text",innerHTML:Z},null,8,ma),ga]))),128)),k("span",ba,[k("span",{class:"text",innerHTML:B.title},null,8,wa)])]),j(y)?(ee(),re("div",xa,[B.text?(ee(),re("div",Fa,[k("div",{class:"vp-doc",innerHTML:B.text},null,8,Ea)])):Se("",!0),Sa,Aa])):Se("",!0)])],42,va)],8,da))),128)),j(v)&&!g.value.length&&S.value?(ee(),re("li",Ta,[ve(pe(j(p)("modal.noResultsText"))+' "',1),k("strong",null,pe(j(v)),1),ve('" ')])):Se("",!0)],40,ha),k("div",Na,[k("span",null,[k("kbd",{"aria-label":j(p)("modal.footer.navigateUpKeyAriaLabel")},Da,8,Ca),k("kbd",{"aria-label":j(p)("modal.footer.navigateDownKeyAriaLabel")},Ra,8,ka),ve(" "+pe(j(p)("modal.footer.navigateText")),1)]),k("span",null,[k("kbd",{"aria-label":j(p)("modal.footer.selectKeyAriaLabel")},La,8,_a),ve(" "+pe(j(p)("modal.footer.selectText")),1)]),k("span",null,[k("kbd",{"aria-label":j(p)("modal.footer.closeKeyAriaLabel")},"esc",8,Pa),ve(" "+pe(j(p)("modal.footer.closeText")),1)])])])],8,qr)])}}}),Ja=tr(za,[["__scopeId","data-v-cb4e1afe"]]);export{Ja as default};
