#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
蓝奏云书籍下载器使用示例
"""

from book_downloader import LanzouBookDownloader
import json


def search_example():
    """搜索示例"""
    downloader = LanzouBookDownloader()
    
    # 搜索书籍
    keyword = "喻青"  # 根据你提供的响应示例
    results = downloader.search_books(keyword)
    
    print(f"搜索关键词: {keyword}")
    print(f"搜索结果:")
    
    for i, book in enumerate(results, 1):
        print(f"{i}. 文件名: {book.get('name_all', 'Unknown')}")
        print(f"   文件大小: {book.get('size', 'Unknown')}")
        print(f"   文件ID: {book.get('id', 'Unknown')}")
        print(f"   文件类型: {book.get('icon', 'Unknown')}")
        print("-" * 50)


def download_example():
    """下载示例"""
    downloader = LanzouBookDownloader()
    
    # 模拟搜索结果（基于你提供的响应）
    sample_book = {
        "id": "iWPTA32ifmne",
        "icon": "txt",
        "name_all": "【BL】《喻青》作者：我将在今夜吻你.txt",
        "size": "1.2 M",
        "duan": "i32ifmn",
        "p_ico": 0,
        "t": "0"
    }
    
    print("下载示例文件...")
    success = downloader.download_file(sample_book, "downloads")
    
    if success:
        print("下载成功！")
    else:
        print("下载失败！")


def interactive_search():
    """交互式搜索和下载"""
    downloader = LanzouBookDownloader()
    
    while True:
        print("\n=== 蓝奏云书籍搜索下载器 ===")
        print("1. 搜索书籍")
        print("2. 搜索并下载")
        print("3. 退出")
        
        choice = input("请选择操作 (1-3): ").strip()
        
        if choice == "1":
            keyword = input("请输入搜索关键词: ").strip()
            if keyword:
                results = downloader.search_books(keyword)
                if results:
                    print(f"\n找到 {len(results)} 个结果:")
                    for i, book in enumerate(results, 1):
                        print(f"{i}. {book.get('name_all', 'Unknown')} ({book.get('size', 'Unknown')})")
                else:
                    print("没有找到相关书籍")
            else:
                print("请输入有效的搜索关键词")
                
        elif choice == "2":
            keyword = input("请输入搜索关键词: ").strip()
            if keyword:
                max_results = input("最大下载数量 (默认10): ").strip()
                try:
                    max_results = int(max_results) if max_results else 10
                except ValueError:
                    max_results = 10
                
                downloader.search_and_download(keyword, max_results=max_results)
            else:
                print("请输入有效的搜索关键词")
                
        elif choice == "3":
            print("再见！")
            break
        else:
            print("无效选择，请重试")


if __name__ == "__main__":
    # 运行交互式搜索
    interactive_search()
    
    # 或者运行单独的示例
    # search_example()
    # download_example()
