import{_ as l,D as t,c as e,I as a,j as s,a as p,a3 as n,o as h}from"./chunks/framework.D42TvibZ.js";const J=JSON.parse('{"title":"V3优享版接口 🆑","description":"","frontmatter":{"title":"V3优享版接口 🆑"},"headers":[],"relativePath":"api-dock-v3.md","filePath":"api-dock-v3.md","lastUpdated":1740580269000}'),k={name:"api-dock-v3.md"},r=s("h1",{id:"lanzou-pro-v3-优享版-接口列表",tabindex:"-1"},[p("Lanzou Pro V3 优享版 接口列表 "),s("a",{class:"header-anchor",href:"#lanzou-pro-v3-优享版-接口列表","aria-label":'Permalink to "Lanzou Pro V3 优享版 接口列表"'},"​")],-1),d=s("hr",null,null,-1),o=s("p",null,"本项目基于 Python3.10 开发、Web框架选用 Flask4.0、文档生成工具 VitePress.",-1),E=s("thead",null,[s("tr",null,[s("th",null,"API"),s("th",{style:{"text-align":"center"}},"状态"),s("th",{style:{"text-align":"center"}},"版本"),s("th",{style:{"text-align":"left"}},"路由")])],-1),c=s("td",null,"获取文件夹ID",-1),u=s("td",{style:{"text-align":"center"}},"✅",-1),g={style:{"text-align":"center"}},y=s("td",{style:{"text-align":"left"}},[s("code",null,"/v3/iGetFolderId/{shareId}/{Page}/{Limit}")],-1),b=s("td",null,"获取文件列表",-1),F=s("td",{style:{"text-align":"center"}},"✅",-1),C={style:{"text-align":"center"}},m=s("td",{style:{"text-align":"left"}},[s("code",null,"/v3/iGetFiles/{shareId}/{folderId}/{Page}/{Limit}")],-1),q=s("td",null,"搜索文件",-1),B=s("td",{style:{"text-align":"center"}},"✅",-1),f={style:{"text-align":"center"}},_=s("td",{style:{"text-align":"left"}},[s("code",null,"/v3/iSearchFile/{shareId}/{folderId}/{Wd}/{Page}/{Limit}")],-1),v=s("td",null,"依文件Id解析",-1),I=s("td",{style:{"text-align":"center"}},"✅",-1),D={style:{"text-align":"center"}},x=s("td",{style:{"text-align":"left"}},[s("code",null,"/v3/iParse/{fileId} (直链地址)")],-1),P=s("td",null,"依文件Id解析",-1),G=s("td",{style:{"text-align":"center"}},"✅",-1),T={style:{"text-align":"center"}},z=s("td",{style:{"text-align":"left"}},[s("code",null,"/v3/iParse301/{fileId} (重定向地址)")],-1),V=n("",22),S=n("",11);function A(j,w,L,R,N,O){const i=t("Badge");return h(),e("div",null,[a(i,{type:"warning",text:"v1.0.5 - For beta",xmlns:"yes"}),r,d,o,s("table",null,[E,s("tbody",null,[s("tr",null,[c,u,s("td",g,[a(i,{type:"tip",text:"^1.0.5"})]),y]),s("tr",null,[b,F,s("td",C,[a(i,{type:"tip",text:"^1.0.5"})]),m]),s("tr",null,[q,B,s("td",f,[a(i,{type:"tip",text:"^1.0.5"})]),_]),s("tr",null,[v,I,s("td",D,[a(i,{type:"tip",text:"^1.0.5"})]),x]),s("tr",null,[P,G,s("td",T,[a(i,{type:"tip",text:"^1.0.0"})]),z])])]),V,a(i,{type:"danger",text:"注：V3版不支持需密码文件解析，有求可用V1版.",xmlns:"yes"}),S])}const Y=l(k,[["render",A]]);export{J as __pageData,Y as default};
