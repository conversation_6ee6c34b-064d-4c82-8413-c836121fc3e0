import{_ as t,c as e,o as i,a3 as l}from"./chunks/framework.D42TvibZ.js";const h=JSON.parse('{"title":"","description":"","frontmatter":{"layout":"home","hero":{"name":"Forever free 🦢","text":"蓝奏云 API 接口服务 :)","tagline":"v1.0.5 - Stable Release. 支持优享版 & 专业版 🚀","image":{"src":"logo.png","alt":"Lanzou API Dock"},"actions":[{"theme":"brand","text":"开始使用 →","link":"/api-dock-v1"},{"theme":"alt","text":"GitHub","link":"https://github.com/uyevan/LanzouProDock"}]},"features":[{"title":"永久免费","icon":"🎯","details":"完全免费、不限速、无需付费，为开发者提供可靠的云盘API服务。"},{"title":"稳定可靠","icon":"⚡","details":"99.9%可用性保证，接口响应迅速，服务稳定持久运行。"},{"title":"安全无忧","icon":"🛡️","details":"采用业界标准加密传输，所有请求数据即时处理，从不存储敏感信息。"},{"title":"简单易用","icon":"🎨","details":"RESTful API设计，详尽的接口文档，降低开发者的学习成本。"},{"title":"专业支持","icon":"🔧","details":"持续更新维护，及时响应issues，解决开发者遇到的问题。"},{"title":"开箱即用","icon":"🚀","details":"提供多语言SDK与示例代码，快速集成到您的项目中。"}]},"headers":[],"relativePath":"index.md","filePath":"index.md","lastUpdated":1740580269000}'),a={name:"index.md"},o=l('<div class="danger custom-block"><p class="custom-block-title">免责声明</p><ul><li>本服务仅供学习交流使用，请勿用于任何违法用途</li><li>使用本服务所产生的任何法律责任和风险，均由使用者自行承担</li><li>我们保留随时终止服务的权利，恕不另行通知</li></ul></div><div class="warning custom-block"><p class="custom-block-title">版权声明</p><ul><li>本项目基于 MIT 开源协议，欢迎贡献代码或提出建议</li><li>使用本项目代码时请遵守开源协议规范</li><li>任何人或组织在遵循协议的前提下可自由使用</li></ul></div>',2),s=[o];function c(n,d,r,_,u,m){return i(),e("div",null,s)}const f=t(a,[["render",c]]);export{h as __pageData,f as default};
