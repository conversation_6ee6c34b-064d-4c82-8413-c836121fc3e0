import{_ as l,D as t,c as e,I as a,j as s,a as p,a3 as n,o as h}from"./chunks/framework.D42TvibZ.js";const J=JSON.parse('{"title":"V3优享版接口 🆑","description":"","frontmatter":{"title":"V3优享版接口 🆑"},"headers":[],"relativePath":"api-dock-v3.md","filePath":"api-dock-v3.md","lastUpdated":1740580269000}'),k={name:"api-dock-v3.md"},r=s("h1",{id:"lanzou-pro-v3-优享版-接口列表",tabindex:"-1"},[p("Lanzou Pro V3 优享版 接口列表 "),s("a",{class:"header-anchor",href:"#lanzou-pro-v3-优享版-接口列表","aria-label":'Permalink to "Lanzou Pro V3 优享版 接口列表"'},"​")],-1),d=s("hr",null,null,-1),o=s("p",null,"本项目基于 Python3.10 开发、Web框架选用 Flask4.0、文档生成工具 VitePress.",-1),E=s("thead",null,[s("tr",null,[s("th",null,"API"),s("th",{style:{"text-align":"center"}},"状态"),s("th",{style:{"text-align":"center"}},"版本"),s("th",{style:{"text-align":"left"}},"路由")])],-1),c=s("td",null,"获取文件夹ID",-1),u=s("td",{style:{"text-align":"center"}},"✅",-1),g={style:{"text-align":"center"}},y=s("td",{style:{"text-align":"left"}},[s("code",null,"/v3/iGetFolderId/{shareId}/{Page}/{Limit}")],-1),b=s("td",null,"获取文件列表",-1),F=s("td",{style:{"text-align":"center"}},"✅",-1),C={style:{"text-align":"center"}},m=s("td",{style:{"text-align":"left"}},[s("code",null,"/v3/iGetFiles/{shareId}/{folderId}/{Page}/{Limit}")],-1),q=s("td",null,"搜索文件",-1),B=s("td",{style:{"text-align":"center"}},"✅",-1),f={style:{"text-align":"center"}},_=s("td",{style:{"text-align":"left"}},[s("code",null,"/v3/iSearchFile/{shareId}/{folderId}/{Wd}/{Page}/{Limit}")],-1),v=s("td",null,"依文件Id解析",-1),I=s("td",{style:{"text-align":"center"}},"✅",-1),D={style:{"text-align":"center"}},x=s("td",{style:{"text-align":"left"}},[s("code",null,"/v3/iParse/{fileId} (直链地址)")],-1),P=s("td",null,"依文件Id解析",-1),G=s("td",{style:{"text-align":"center"}},"✅",-1),T={style:{"text-align":"center"}},z=s("td",{style:{"text-align":"left"}},[s("code",null,"/v3/iParse301/{fileId} (重定向地址)")],-1),V=n(`<h3 id="快速入门指南-🚀" tabindex="-1">快速入门指南 🚀 <a class="header-anchor" href="#快速入门指南-🚀" aria-label="Permalink to &quot;快速入门指南 🚀&quot;">​</a></h3><div class="tip custom-block"><p class="custom-block-title">参数获取流程</p><p>以下是获取各类 ID 的基本流程和说明：</p><h4 id="_1️⃣-分享id-shareid" tabindex="-1">1️⃣ 分享ID (shareId) <a class="header-anchor" href="#_1️⃣-分享id-shareid" aria-label="Permalink to &quot;1️⃣ 分享ID (shareId)&quot;">​</a></h4><ul><li>形如：<code>s0bJGkc</code></li><li>位置：优享版分享链接的最后部分</li><li>示例：<code>https://www.ilanzou.com/s/s0bJGkc</code> 中的 <code>s0bJGkc</code></li><li>用途：所有 API 的基础参数</li></ul><h4 id="_2️⃣-文件夹id-folderid" tabindex="-1">2️⃣ 文件夹ID (folderId) <a class="header-anchor" href="#_2️⃣-文件夹id-folderid" aria-label="Permalink to &quot;2️⃣ 文件夹ID (folderId)&quot;">​</a></h4><p>获取方式有两种：</p><ol><li>通过 API：调用 <code>iGetFolderId</code> 接口</li><li>手动获取：浏览器调试模式查看网络请求</li></ol><h4 id="_3️⃣-文件id-fileid" tabindex="-1">3️⃣ 文件ID (fileId) <a class="header-anchor" href="#_3️⃣-文件id-fileid" aria-label="Permalink to &quot;3️⃣ 文件ID (fileId)&quot;">​</a></h4><p>获取方式：</p><ol><li>调用 <code>iGetFiles</code> 接口（需要 shareId 和 folderId）</li><li>对于单文件分享，可直接通过 <code>iGetFolderId</code> 获取</li></ol></div><h3 id="接口文档-📇" tabindex="-1">接口文档 📇 <a class="header-anchor" href="#接口文档-📇" aria-label="Permalink to &quot;接口文档 📇&quot;">​</a></h3><h4 id="🤡-获取文件夹id-igetfolderid" tabindex="-1">🤡 获取文件夹ID：iGetFolderId <a class="header-anchor" href="#🤡-获取文件夹id-igetfolderid" aria-label="Permalink to &quot;🤡 获取文件夹ID：iGetFolderId&quot;">​</a></h4><ul><li><strong>路径</strong>：<code>/iGetFolderId</code></li><li><strong>请求方法</strong>：<code>GET</code></li><li><strong>请求参数</strong>： <ul><li><code>shareId</code>: 分享ID，优享版分享链接最后的字符串ID；如：<code>ilanzou.com\\s\\[shareId]</code></li><li><code>page</code>: 页码</li><li><code>limit</code>: 每页对应文件数量</li></ul></li><li><strong>返回实例</strong>：</li></ul><div class="language-json vp-adaptive-theme line-numbers-mode"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;code&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">200</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;folders&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: [</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    {</span></span>
<span class="line"><span style="--shiki-light:#B31D28;--shiki-dark:#FDAEB7;--shiki-light-font-style:italic;--shiki-dark-font-style:italic;">      ...</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileList&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: [</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        {</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">          &quot;fileType&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">2</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">          &quot;folderDesc&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;这是一个文件夹噢~&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">          &quot;folderIcon&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;assets/images/tab_file/icon/folder.png&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">          &quot;folderId&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">26269065</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">          &quot;folderName&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;个人作品另存图&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">          &quot;type&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">1</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">          &quot;updTime&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;2024-12-11 00:12:20&quot;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        }</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      ],</span></span>
<span class="line"><span style="--shiki-light:#B31D28;--shiki-dark:#FDAEB7;--shiki-light-font-style:italic;--shiki-dark-font-style:italic;">      ...</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    }</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  ],</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;status&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;获取成功&quot;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre><div class="line-numbers-wrapper" aria-hidden="true"><span class="line-number">1</span><br><span class="line-number">2</span><br><span class="line-number">3</span><br><span class="line-number">4</span><br><span class="line-number">5</span><br><span class="line-number">6</span><br><span class="line-number">7</span><br><span class="line-number">8</span><br><span class="line-number">9</span><br><span class="line-number">10</span><br><span class="line-number">11</span><br><span class="line-number">12</span><br><span class="line-number">13</span><br><span class="line-number">14</span><br><span class="line-number">15</span><br><span class="line-number">16</span><br><span class="line-number">17</span><br><span class="line-number">18</span><br><span class="line-number">19</span><br><span class="line-number">20</span><br><span class="line-number">21</span><br></div></div><div class="info custom-block"><p class="custom-block-title">路径说明</p><p>获取指定链接下的所有文件夹和文件信息。</p></div><div class="danger custom-block"><p class="custom-block-title">请求URL实例</p><p><a href="https://lanzou.uyclouds.com/v3/iGetFolderId/YRVyOZDp/1/30" target="_blank" rel="noreferrer">https://lanzou.uyclouds.com/v3/iGetFolderId/YRVyOZDp/1/30</a></p></div><hr><h4 id="📂-获取文件列表-igetfiles" tabindex="-1">📂 获取文件列表：iGetFiles <a class="header-anchor" href="#📂-获取文件列表-igetfiles" aria-label="Permalink to &quot;📂 获取文件列表：iGetFiles&quot;">​</a></h4><ul><li><strong>路径</strong>：<code>/iGetFiles</code></li><li><strong>请求方法</strong>：<code>GET</code></li><li><strong>请求参数</strong>： <ul><li><code>shareId</code>: 分享ID，优享版分享链接最后的字符串ID；如：<code>ilanzou.com\\s\\[shareId]</code></li><li><code>folderId</code>: 文件夹ID，每个分享的链接必定会有一个文件夹，可以通过第一个接口获取主文件夹ID；</li><li><code>page</code>: 页码</li><li><code>limit</code>: 每页对应文件数量</li></ul></li><li><strong>返回实例</strong>：</li></ul><div class="language-json vp-adaptive-theme line-numbers-mode"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;code&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">200</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;status&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;获取成功&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;files&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: [</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    {</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileComments&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileDownloads&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileIcon&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;https://image.webgetstore.com/202412110013/a553d4e4de08af5aae92bbc2f2253e7b/disk/icon/2024/12/10/115782/8145897345576944.rar&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileId&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">2631771977</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileLikes&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileName&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;head1.jpg&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileSaves&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileSize&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">116</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileStars&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">5.0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileType&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">1</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileUrl&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">null</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;iconId&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">16</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;name&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;head1.jpg&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;sortId&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">2631771977</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;type&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">1</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;updTime&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;2024-12-11 00:12:20&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;userId&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">2806070</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    },</span></span>
<span class="line"><span style="--shiki-light:#B31D28;--shiki-dark:#FDAEB7;--shiki-light-font-style:italic;--shiki-dark-font-style:italic;">    ...</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  ]</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre><div class="line-numbers-wrapper" aria-hidden="true"><span class="line-number">1</span><br><span class="line-number">2</span><br><span class="line-number">3</span><br><span class="line-number">4</span><br><span class="line-number">5</span><br><span class="line-number">6</span><br><span class="line-number">7</span><br><span class="line-number">8</span><br><span class="line-number">9</span><br><span class="line-number">10</span><br><span class="line-number">11</span><br><span class="line-number">12</span><br><span class="line-number">13</span><br><span class="line-number">14</span><br><span class="line-number">15</span><br><span class="line-number">16</span><br><span class="line-number">17</span><br><span class="line-number">18</span><br><span class="line-number">19</span><br><span class="line-number">20</span><br><span class="line-number">21</span><br><span class="line-number">22</span><br><span class="line-number">23</span><br><span class="line-number">24</span><br><span class="line-number">25</span><br><span class="line-number">26</span><br></div></div><div class="info custom-block"><p class="custom-block-title">路径说明</p><p>获取指定链接下的所有文件夹信息。</p></div><div class="danger custom-block"><p class="custom-block-title">请求URL实例</p><p><a href="https://lanzou.uyclouds.com/v3/iGetFiles/YRVyOZDp/26269065/1/30" target="_blank" rel="noreferrer">https://lanzou.uyclouds.com/v3/iGetFiles/YRVyOZDp/26269065/1/30</a></p></div><hr><h4 id="📄-搜索文件-isearchfile" tabindex="-1">📄 搜索文件：iSearchFile <a class="header-anchor" href="#📄-搜索文件-isearchfile" aria-label="Permalink to &quot;📄 搜索文件：iSearchFile&quot;">​</a></h4><ul><li><strong>路径</strong>：<code>/iSearchFile</code></li><li><strong>请求方法</strong>：<code>GET</code></li><li><strong>请求参数</strong>： <ul><li><code>shareId</code>: 分享ID，优享版分享链接最后的字符串ID；如：<code>ilanzou.com\\s\\[shareId]</code></li><li><code>folderId</code>: 文件夹ID，每个分享的链接必定会有一个文件夹，可以通过第一个接口获取主文件夹ID；</li><li><code>wd</code>: 搜索关键词；</li><li><code>page</code>: 页码</li><li><code>limit</code>: 每页对应文件数量</li></ul></li><li><strong>返回实例</strong>：</li></ul><div class="language-json vp-adaptive-theme line-numbers-mode"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;code&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">200</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;status&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;获取成功&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;files&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: [</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    {</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileComments&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileDownloads&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileIcon&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;https://image.webgetstore.com/202412110013/9deaaba8aab4bf44aec7056dabba0944/disk/icon/2024/12/10/115782/8145897264926055.rar&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileId&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">2631771975</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileLikes&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileName&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;head2.jpg&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileSaves&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileSize&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">94</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileStars&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">5.0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileType&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">1</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileUrl&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">null</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;iconId&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">16</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;name&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;head1.jpg&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;sortId&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">2631771975</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;type&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">1</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;updTime&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;2024-12-11 00:12:19&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;userId&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">2806070</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    },</span></span>
<span class="line"><span style="--shiki-light:#B31D28;--shiki-dark:#FDAEB7;--shiki-light-font-style:italic;--shiki-dark-font-style:italic;">    ...</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  ]</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre><div class="line-numbers-wrapper" aria-hidden="true"><span class="line-number">1</span><br><span class="line-number">2</span><br><span class="line-number">3</span><br><span class="line-number">4</span><br><span class="line-number">5</span><br><span class="line-number">6</span><br><span class="line-number">7</span><br><span class="line-number">8</span><br><span class="line-number">9</span><br><span class="line-number">10</span><br><span class="line-number">11</span><br><span class="line-number">12</span><br><span class="line-number">13</span><br><span class="line-number">14</span><br><span class="line-number">15</span><br><span class="line-number">16</span><br><span class="line-number">17</span><br><span class="line-number">18</span><br><span class="line-number">19</span><br><span class="line-number">20</span><br><span class="line-number">21</span><br><span class="line-number">22</span><br><span class="line-number">23</span><br><span class="line-number">24</span><br><span class="line-number">25</span><br><span class="line-number">26</span><br></div></div><div class="info custom-block"><p class="custom-block-title">路径说明</p><p>获取指定链接下的所有文件信息。</p></div><div class="danger custom-block"><p class="custom-block-title">请求URL实例</p><p><a href="https://lanzou.uyclouds.com/v3/iSearchFile/YRVyOZDp/26269065/head/1/30" target="_blank" rel="noreferrer">https://lanzou.uyclouds.com/v3/iSearchFile/YRVyOZDp/26269065/head/1/30</a></p></div><hr><h4 id="🆔-依文件id解析-返回文件下载直链地址-iparse" tabindex="-1">🆔 依文件Id解析（返回文件下载直链地址）：iParse <a class="header-anchor" href="#🆔-依文件id解析-返回文件下载直链地址-iparse" aria-label="Permalink to &quot;🆔 依文件Id解析（返回文件下载直链地址）：iParse&quot;">​</a></h4>`,22),S=n(`<ul><li><strong>路径</strong>：<code>/iParse</code></li><li><strong>请求方法</strong>：<code>GET</code>、<code>POST</code></li><li><strong>请求参数</strong>： <ul><li><code>fileId</code>：需要解析的蓝奏云优享版文件fileId，必填。</li></ul></li><li><strong>返回实例</strong>：</li></ul><div class="language-json vp-adaptive-theme line-numbers-mode"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;code&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">200</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;status&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;解析成功&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;url&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;https://small4.bakstotre.com/0609263a24c226a103591742da632ad3/67bf28f9/2024/03/22/1e9f66ac045803bc84d2ffad7631bcf3.txt?fn=1-2224%E6%AD%BB%E7%81%B5%E6%B3%95%E5%B8%88.txt&quot;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre><div class="line-numbers-wrapper" aria-hidden="true"><span class="line-number">1</span><br><span class="line-number">2</span><br><span class="line-number">3</span><br><span class="line-number">4</span><br><span class="line-number">5</span><br></div></div><div class="danger custom-block"><p class="custom-block-title">请求URL实例</p><p><a href="https://lanzou.uyclouds.com/v3/iParse/277276043" target="_blank" rel="noreferrer">https://lanzou.uyclouds.com/v3/iParse/277276043</a></p></div><h4 id="🆔-依文件id解析-返回文件下载301重定向地址-iparse301" tabindex="-1">🆔 依文件Id解析（返回文件下载301重定向地址）：iParse301 <a class="header-anchor" href="#🆔-依文件id解析-返回文件下载301重定向地址-iparse301" aria-label="Permalink to &quot;🆔 依文件Id解析（返回文件下载301重定向地址）：iParse301&quot;">​</a></h4><ul><li><strong>路径</strong>：<code>/iParse301/{fileId}</code></li><li><strong>请求方法</strong>：<code>GET</code></li><li><strong>请求参数</strong>： <ul><li><code>fileId</code>：需要解析的蓝奏云优享版文件fileId，必填。</li></ul></li><li><strong>返回实例</strong>：</li></ul><div class="language-json vp-adaptive-theme line-numbers-mode"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;code&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">200</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;status&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;解析成功&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;url&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;https://api.ilanzou.com/unproved/file/redirect?downloadId=B303DB89B576288D147D469BF5318E2F&amp;enable=1&amp;devType=6&amp;uuid=7zEjxeRCxQrra560eTL8L&amp;timestamp=E038886D007E47A5BD0330267752D053&amp;auth=8C66AC14C8C450B78E8AEDF37A424C6BB5E140D21FC29563664D2B54BA404D5D&amp;shareId=8RTWCKF&quot;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre><div class="line-numbers-wrapper" aria-hidden="true"><span class="line-number">1</span><br><span class="line-number">2</span><br><span class="line-number">3</span><br><span class="line-number">4</span><br><span class="line-number">5</span><br></div></div><div class="danger custom-block"><p class="custom-block-title">请求URL实例</p><p><a href="https://lanzou.uyclouds.com/v3/iParse301/277276043" target="_blank" rel="noreferrer">https://lanzou.uyclouds.com/v3/iParse301/277276043</a></p></div><div class="info custom-block"><p class="custom-block-title">建议使用 <code>iParse</code> 接口，它会直接返回解析后的下载地址，而 <code>iParse301</code> 接口返回的是301重定向地址。</p></div><h3 id="📝-使用示例" tabindex="-1">📝 使用示例 <a class="header-anchor" href="#📝-使用示例" aria-label="Permalink to &quot;📝 使用示例&quot;">​</a></h3><div class="info custom-block"><p class="custom-block-title">文件夹分享场景</p><ul><li>第一步：获取文件夹ID</li></ul><p>GET /v3/iGetFolderId/s0bJGkc/1/30</p><ul><li>第二步：获取文件列表</li></ul><p>GET /v3/iGetFiles/s0bJGkc/26269065/1/30</p><ul><li>第三步：解析特定文件</li></ul><p>GET /v3/iParse/2631771977</p></div><div class="info custom-block"><p class="custom-block-title">单文件分享场景</p><ul><li>直接获取文件ID</li></ul><p>GET /v3/iGetFolderId/s0bJGkc/1/30</p><ul><li>解析下载链接</li></ul><p>GET /v3/iParse/2631771977</p></div>`,11);function A(j,w,L,R,N,O){const i=t("Badge");return h(),e("div",null,[a(i,{type:"warning",text:"v1.0.5 - For beta",xmlns:"yes"}),r,d,o,s("table",null,[E,s("tbody",null,[s("tr",null,[c,u,s("td",g,[a(i,{type:"tip",text:"^1.0.5"})]),y]),s("tr",null,[b,F,s("td",C,[a(i,{type:"tip",text:"^1.0.5"})]),m]),s("tr",null,[q,B,s("td",f,[a(i,{type:"tip",text:"^1.0.5"})]),_]),s("tr",null,[v,I,s("td",D,[a(i,{type:"tip",text:"^1.0.5"})]),x]),s("tr",null,[P,G,s("td",T,[a(i,{type:"tip",text:"^1.0.0"})]),z])])]),V,a(i,{type:"danger",text:"注：V3版不支持需密码文件解析，有求可用V1版.",xmlns:"yes"}),S])}const Y=l(k,[["render",A]]);export{J as __pageData,Y as default};
