[uwsgi]
#项目目录
chdir=/www/wwwroot/LanzouAPI

#指定项目application
wsgi-file=/www/wwwroot/LanzouAPI/application.py

# 进程个数
processes=4

# 线程个数
threads=2

#指定启动时的pid文件路径（用于停止服务和重启服务，请勿删除）
pidfile=/www/wwwroot/LanzouAPI/uwsgi.pid

# 指定ip及端口
# 默认http模式，可切换至socket模式
http=0.0.0.0:3307
#socket=0.0.0.0:3307

#启动uwsgi的用户名和用户组
uid=www
gid=www

#启用主进程
master=true

# 后台运行,并输出日志
daemonize = /www/wwwlogs/python/LanzouAPI/uwsgi.log

# 自定义设置项请写到该处, 
# 如果项目的启动方式您不想使用 wsgi-file 请注释掉,但不要删除，通讯方式(http, socket)同理;
# 最好以上面相同的格式 <注释 + 换行 + key = value> 进行书写，方便以后查阅
