#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
蓝奏云书籍搜索下载工具
支持搜索和下载蓝奏云上的书籍文件
"""

import requests
import json
import re
import os
from urllib.parse import urljoin, urlparse
from typing import Dict, List, Optional
import time


class LanzouBookDownloader:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://baoz.lanzn.com"
        self.search_url = "https://baoz.lanzn.com/search/s.php"
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'Accept': 'application/json, text/javascript, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest',
            'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
        })

    def search_books(self, keyword: str, folder_id: str = "03kvs06j") -> List[Dict]:
        """
        搜索书籍
        
        Args:
            keyword: 搜索关键词
            folder_id: 文件夹ID，默认为提供的文件夹
            
        Returns:
            搜索结果列表
        """
        # 构造搜索数据
        search_data = {
            'search_task': '12',
            'search_text': keyword,
            'search_folder_id': folder_id,
            'search_ext': '',
            'search_size_min': '',
            'search_size_max': ''
        }
        
        try:
            # 设置referer
            self.session.headers['Referer'] = f"https://baoz.lanzn.com/b{folder_id}"
            self.session.headers['Origin'] = self.base_url
            
            response = self.session.post(self.search_url, data=search_data)
            response.raise_for_status()
            
            # 解析JSON响应
            if response.text.strip():
                result = response.json()
                if isinstance(result, dict):
                    return [result]
                elif isinstance(result, list):
                    return result
                else:
                    print(f"未知的响应格式: {result}")
                    return []
            else:
                print("搜索返回空结果")
                return []
                
        except requests.exceptions.RequestException as e:
            print(f"搜索请求失败: {e}")
            return []
        except json.JSONDecodeError as e:
            print(f"解析搜索结果失败: {e}")
            print(f"响应内容: {response.text}")
            return []

    def get_download_link(self, file_id: str) -> Optional[str]:
        """
        获取文件的真实下载链接
        
        Args:
            file_id: 文件ID
            
        Returns:
            下载链接或None
        """
        try:
            # 访问文件页面
            file_url = f"{self.base_url}/{file_id}"
            response = self.session.get(file_url)
            response.raise_for_status()
            
            # 从页面中提取下载链接
            # 这里需要根据蓝奏云的实际页面结构来解析
            # 通常需要解析JavaScript或者表单来获取真实下载链接
            
            # 简化版本：直接返回文件URL，实际使用时可能需要更复杂的解析
            return file_url
            
        except requests.exceptions.RequestException as e:
            print(f"获取下载链接失败: {e}")
            return None

    def download_file(self, file_info: Dict, download_dir: str = "downloads") -> bool:
        """
        下载文件
        
        Args:
            file_info: 文件信息字典
            download_dir: 下载目录
            
        Returns:
            下载是否成功
        """
        try:
            # 创建下载目录
            os.makedirs(download_dir, exist_ok=True)
            
            file_id = file_info.get('id')
            file_name = file_info.get('name_all', f"unknown_{file_id}")
            
            # 清理文件名中的非法字符
            file_name = re.sub(r'[<>:"/\\|?*]', '_', file_name)
            
            print(f"开始下载: {file_name}")
            
            # 获取下载链接
            download_url = self.get_download_link(file_id)
            if not download_url:
                print(f"无法获取下载链接: {file_name}")
                return False
            
            # 下载文件
            response = self.session.get(download_url, stream=True)
            response.raise_for_status()
            
            file_path = os.path.join(download_dir, file_name)
            
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            print(f"下载完成: {file_path}")
            return True
            
        except Exception as e:
            print(f"下载失败: {e}")
            return False

    def search_and_download(self, keyword: str, download_dir: str = "downloads", max_results: int = 10):
        """
        搜索并下载书籍
        
        Args:
            keyword: 搜索关键词
            download_dir: 下载目录
            max_results: 最大下载数量
        """
        print(f"搜索关键词: {keyword}")
        
        # 搜索书籍
        results = self.search_books(keyword)
        
        if not results:
            print("没有找到相关书籍")
            return
        
        print(f"找到 {len(results)} 个结果")
        
        # 显示搜索结果
        for i, book in enumerate(results[:max_results], 1):
            print(f"{i}. {book.get('name_all', 'Unknown')} ({book.get('size', 'Unknown size')})")
        
        # 下载文件
        downloaded = 0
        for book in results[:max_results]:
            if self.download_file(book, download_dir):
                downloaded += 1
            time.sleep(1)  # 避免请求过于频繁
        
        print(f"成功下载 {downloaded} 个文件")


def main():
    """主函数"""
    downloader = LanzouBookDownloader()
    
    # 示例使用
    keyword = input("请输入搜索关键词: ").strip()
    if keyword:
        downloader.search_and_download(keyword)
    else:
        print("请输入有效的搜索关键词")


if __name__ == "__main__":
    main()
