# 蓝奏云书籍搜索下载器

这是一个用于搜索和下载蓝奏云书籍的Python工具。

## 功能特性

- 🔍 支持关键词搜索书籍
- 📥 自动下载搜索到的书籍文件
- 🎯 支持指定文件夹搜索
- 📊 显示文件大小和类型信息
- 🛡️ 自动处理请求头和会话管理

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 基本使用

```python
from book_downloader import LanzouBookDownloader

# 创建下载器实例
downloader = LanzouBookDownloader()

# 搜索书籍
results = downloader.search_books("喻青")

# 下载第一个搜索结果
if results:
    downloader.download_file(results[0])
```

### 2. 交互式使用

运行交互式程序：

```bash
python example_usage.py
```

### 3. 直接搜索并下载

```python
from book_downloader import LanzouBookDownloader

downloader = LanzouBookDownloader()
downloader.search_and_download("关键词", max_results=5)
```

## API 说明

### LanzouBookDownloader 类

#### 方法

- `search_books(keyword, folder_id="03kvs06j")`: 搜索书籍
  - `keyword`: 搜索关键词
  - `folder_id`: 文件夹ID（默认使用提供的文件夹）
  - 返回: 搜索结果列表

- `download_file(file_info, download_dir="downloads")`: 下载文件
  - `file_info`: 文件信息字典
  - `download_dir`: 下载目录
  - 返回: 下载是否成功

- `search_and_download(keyword, download_dir="downloads", max_results=10)`: 搜索并下载
  - `keyword`: 搜索关键词
  - `download_dir`: 下载目录
  - `max_results`: 最大下载数量

## 搜索结果格式

搜索返回的每个结果包含以下字段：

```json
{
  "id": "文件ID",
  "icon": "文件类型图标",
  "name_all": "完整文件名",
  "size": "文件大小",
  "duan": "短链接标识",
  "p_ico": "图标类型",
  "t": "时间戳"
}
```

## 注意事项

1. **请求频率**: 代码中已添加延时，避免请求过于频繁
2. **文件名处理**: 自动清理文件名中的非法字符
3. **错误处理**: 包含完整的异常处理机制
4. **下载目录**: 默认下载到 `downloads` 文件夹

## 示例

### 搜索示例

```python
downloader = LanzouBookDownloader()
results = downloader.search_books("Python")

for book in results:
    print(f"书名: {book['name_all']}")
    print(f"大小: {book['size']}")
    print(f"ID: {book['id']}")
```

### 下载示例

```python
# 基于提供的响应示例
sample_book = {
    "id": "iWPTA32ifmne",
    "icon": "txt",
    "name_all": "【BL】《喻青》作者：我将在今夜吻你.txt",
    "size": "1.2 M",
    "duan": "i32ifmn",
    "p_ico": 0,
    "t": "0"
}

downloader.download_file(sample_book)
```

## 免责声明

本工具仅供学习和研究使用，请遵守相关网站的使用条款和版权规定。用户应对其使用行为承担全部责任。

## 许可证

MIT License
