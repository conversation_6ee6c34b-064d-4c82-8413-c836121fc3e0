/**
* @vue/shared v3.4.27
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function wr(e,t){const n=new Set(e.split(","));return r=>n.has(r)}const ee={},mt=[],xe=()=>{},Ti=()=>!1,kt=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Er=e=>e.startsWith("onUpdate:"),ie=Object.assign,Cr=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Ai=Object.prototype.hasOwnProperty,Y=(e,t)=>Ai.call(e,t),k=Array.isArray,yt=e=>xn(e)==="[object Map]",zs=e=>xn(e)==="[object Set]",K=e=>typeof e=="function",se=e=>typeof e=="string",ft=e=>typeof e=="symbol",Z=e=>e!==null&&typeof e=="object",Xs=e=>(Z(e)||K(e))&&K(e.then)&&K(e.catch),Ys=Object.prototype.toString,xn=e=>Ys.call(e),Ri=e=>xn(e).slice(8,-1),Js=e=>xn(e)==="[object Object]",xr=e=>se(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,_t=wr(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Sn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Oi=/-(\w)/g,$e=Sn(e=>e.replace(Oi,(t,n)=>n?n.toUpperCase():"")),Li=/\B([A-Z])/g,dt=Sn(e=>e.replace(Li,"-$1").toLowerCase()),Tn=Sn(e=>e.charAt(0).toUpperCase()+e.slice(1)),un=Sn(e=>e?`on${Tn(e)}`:""),Je=(e,t)=>!Object.is(e,t),fn=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},Qs=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},lr=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Ii=e=>{const t=se(e)?Number(e):NaN;return isNaN(t)?e:t};let Qr;const Zs=()=>Qr||(Qr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Sr(e){if(k(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],s=se(r)?Fi(r):Sr(r);if(s)for(const o in s)t[o]=s[o]}return t}else if(se(e)||Z(e))return e}const Mi=/;(?![^(]*\))/g,Pi=/:([^]+)/,Ni=/\/\*[^]*?\*\//g;function Fi(e){const t={};return e.replace(Ni,"").split(Mi).forEach(n=>{if(n){const r=n.split(Pi);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function Tr(e){let t="";if(se(e))t=e;else if(k(e))for(let n=0;n<e.length;n++){const r=Tr(e[n]);r&&(t+=r+" ")}else if(Z(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const $i="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Hi=wr($i);function eo(e){return!!e||e===""}const Za=e=>se(e)?e:e==null?"":k(e)||Z(e)&&(e.toString===Ys||!K(e.toString))?JSON.stringify(e,to,2):String(e),to=(e,t)=>t&&t.__v_isRef?to(e,t.value):yt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,s],o)=>(n[Bn(r,o)+" =>"]=s,n),{})}:zs(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Bn(n))}:ft(t)?Bn(t):Z(t)&&!k(t)&&!Js(t)?String(t):t,Bn=(e,t="")=>{var n;return ft(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.4.27
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let we;class ji{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this.parent=we,!t&&we&&(this.index=(we.scopes||(we.scopes=[])).push(this)-1)}get active(){return this._active}run(t){if(this._active){const n=we;try{return we=this,t()}finally{we=n}}}on(){we=this}off(){we=this.parent}stop(t){if(this._active){let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.scopes)for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);if(!this.detached&&this.parent&&!t){const s=this.parent.scopes.pop();s&&s!==this&&(this.parent.scopes[this.index]=s,s.index=this.index)}this.parent=void 0,this._active=!1}}}function Vi(e,t=we){t&&t.active&&t.effects.push(e)}function no(){return we}function Di(e){we&&we.cleanups.push(e)}let ct;class Ar{constructor(t,n,r,s){this.fn=t,this.trigger=n,this.scheduler=r,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,Vi(this,s)}get dirty(){if(this._dirtyLevel===2||this._dirtyLevel===3){this._dirtyLevel=1,Ze();for(let t=0;t<this._depsLength;t++){const n=this.deps[t];if(n.computed&&(Ui(n.computed),this._dirtyLevel>=4))break}this._dirtyLevel===1&&(this._dirtyLevel=0),et()}return this._dirtyLevel>=4}set dirty(t){this._dirtyLevel=t?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let t=Xe,n=ct;try{return Xe=!0,ct=this,this._runnings++,Zr(this),this.fn()}finally{es(this),this._runnings--,ct=n,Xe=t}}stop(){this.active&&(Zr(this),es(this),this.onStop&&this.onStop(),this.active=!1)}}function Ui(e){return e.value}function Zr(e){e._trackId++,e._depsLength=0}function es(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)ro(e.deps[t],e);e.deps.length=e._depsLength}}function ro(e,t){const n=e.get(t);n!==void 0&&t._trackId!==n&&(e.delete(t),e.size===0&&e.cleanup())}let Xe=!0,cr=0;const so=[];function Ze(){so.push(Xe),Xe=!1}function et(){const e=so.pop();Xe=e===void 0?!0:e}function Rr(){cr++}function Or(){for(cr--;!cr&&ar.length;)ar.shift()()}function oo(e,t,n){if(t.get(e)!==e._trackId){t.set(e,e._trackId);const r=e.deps[e._depsLength];r!==t?(r&&ro(r,e),e.deps[e._depsLength++]=t):e._depsLength++}}const ar=[];function io(e,t,n){Rr();for(const r of e.keys()){let s;r._dirtyLevel<t&&(s??(s=e.get(r)===r._trackId))&&(r._shouldSchedule||(r._shouldSchedule=r._dirtyLevel===0),r._dirtyLevel=t),r._shouldSchedule&&(s??(s=e.get(r)===r._trackId))&&(r.trigger(),(!r._runnings||r.allowRecurse)&&r._dirtyLevel!==2&&(r._shouldSchedule=!1,r.scheduler&&ar.push(r.scheduler)))}Or()}const lo=(e,t)=>{const n=new Map;return n.cleanup=e,n.computed=t,n},mn=new WeakMap,at=Symbol(""),ur=Symbol("");function ve(e,t,n){if(Xe&&ct){let r=mn.get(e);r||mn.set(e,r=new Map);let s=r.get(n);s||r.set(n,s=lo(()=>r.delete(n))),oo(ct,s)}}function Ve(e,t,n,r,s,o){const i=mn.get(e);if(!i)return;let l=[];if(t==="clear")l=[...i.values()];else if(n==="length"&&k(e)){const c=Number(r);i.forEach((a,f)=>{(f==="length"||!ft(f)&&f>=c)&&l.push(a)})}else switch(n!==void 0&&l.push(i.get(n)),t){case"add":k(e)?xr(n)&&l.push(i.get("length")):(l.push(i.get(at)),yt(e)&&l.push(i.get(ur)));break;case"delete":k(e)||(l.push(i.get(at)),yt(e)&&l.push(i.get(ur)));break;case"set":yt(e)&&l.push(i.get(at));break}Rr();for(const c of l)c&&io(c,4);Or()}function Bi(e,t){const n=mn.get(e);return n&&n.get(t)}const ki=wr("__proto__,__v_isRef,__isVue"),co=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(ft)),ts=Ki();function Ki(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...n){const r=J(this);for(let o=0,i=this.length;o<i;o++)ve(r,"get",o+"");const s=r[t](...n);return s===-1||s===!1?r[t](...n.map(J)):s}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...n){Ze(),Rr();const r=J(this)[t].apply(this,n);return Or(),et(),r}}),e}function Wi(e){ft(e)||(e=String(e));const t=J(this);return ve(t,"has",e),t.hasOwnProperty(e)}class ao{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){const s=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!s;if(n==="__v_isReadonly")return s;if(n==="__v_isShallow")return o;if(n==="__v_raw")return r===(s?o?sl:po:o?ho:fo).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const i=k(t);if(!s){if(i&&Y(ts,n))return Reflect.get(ts,n,r);if(n==="hasOwnProperty")return Wi}const l=Reflect.get(t,n,r);return(ft(n)?co.has(n):ki(n))||(s||ve(t,"get",n),o)?l:de(l)?i&&xr(n)?l:l.value:Z(l)?s?On(l):Rn(l):l}}class uo extends ao{constructor(t=!1){super(!1,t)}set(t,n,r,s){let o=t[n];if(!this._isShallow){const c=$t(o);if(!yn(r)&&!$t(r)&&(o=J(o),r=J(r)),!k(t)&&de(o)&&!de(r))return c?!1:(o.value=r,!0)}const i=k(t)&&xr(n)?Number(n)<t.length:Y(t,n),l=Reflect.set(t,n,r,s);return t===J(s)&&(i?Je(r,o)&&Ve(t,"set",n,r):Ve(t,"add",n,r)),l}deleteProperty(t,n){const r=Y(t,n);t[n];const s=Reflect.deleteProperty(t,n);return s&&r&&Ve(t,"delete",n,void 0),s}has(t,n){const r=Reflect.has(t,n);return(!ft(n)||!co.has(n))&&ve(t,"has",n),r}ownKeys(t){return ve(t,"iterate",k(t)?"length":at),Reflect.ownKeys(t)}}class qi extends ao{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Gi=new uo,zi=new qi,Xi=new uo(!0);const Lr=e=>e,An=e=>Reflect.getPrototypeOf(e);function Yt(e,t,n=!1,r=!1){e=e.__v_raw;const s=J(e),o=J(t);n||(Je(t,o)&&ve(s,"get",t),ve(s,"get",o));const{has:i}=An(s),l=r?Lr:n?Pr:Ht;if(i.call(s,t))return l(e.get(t));if(i.call(s,o))return l(e.get(o));e!==s&&e.get(t)}function Jt(e,t=!1){const n=this.__v_raw,r=J(n),s=J(e);return t||(Je(e,s)&&ve(r,"has",e),ve(r,"has",s)),e===s?n.has(e):n.has(e)||n.has(s)}function Qt(e,t=!1){return e=e.__v_raw,!t&&ve(J(e),"iterate",at),Reflect.get(e,"size",e)}function ns(e){e=J(e);const t=J(this);return An(t).has.call(t,e)||(t.add(e),Ve(t,"add",e,e)),this}function rs(e,t){t=J(t);const n=J(this),{has:r,get:s}=An(n);let o=r.call(n,e);o||(e=J(e),o=r.call(n,e));const i=s.call(n,e);return n.set(e,t),o?Je(t,i)&&Ve(n,"set",e,t):Ve(n,"add",e,t),this}function ss(e){const t=J(this),{has:n,get:r}=An(t);let s=n.call(t,e);s||(e=J(e),s=n.call(t,e)),r&&r.call(t,e);const o=t.delete(e);return s&&Ve(t,"delete",e,void 0),o}function os(){const e=J(this),t=e.size!==0,n=e.clear();return t&&Ve(e,"clear",void 0,void 0),n}function Zt(e,t){return function(r,s){const o=this,i=o.__v_raw,l=J(i),c=t?Lr:e?Pr:Ht;return!e&&ve(l,"iterate",at),i.forEach((a,f)=>r.call(s,c(a),c(f),o))}}function en(e,t,n){return function(...r){const s=this.__v_raw,o=J(s),i=yt(o),l=e==="entries"||e===Symbol.iterator&&i,c=e==="keys"&&i,a=s[e](...r),f=n?Lr:t?Pr:Ht;return!t&&ve(o,"iterate",c?ur:at),{next(){const{value:h,done:m}=a.next();return m?{value:h,done:m}:{value:l?[f(h[0]),f(h[1])]:f(h),done:m}},[Symbol.iterator](){return this}}}}function Ue(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Yi(){const e={get(o){return Yt(this,o)},get size(){return Qt(this)},has:Jt,add:ns,set:rs,delete:ss,clear:os,forEach:Zt(!1,!1)},t={get(o){return Yt(this,o,!1,!0)},get size(){return Qt(this)},has:Jt,add:ns,set:rs,delete:ss,clear:os,forEach:Zt(!1,!0)},n={get(o){return Yt(this,o,!0)},get size(){return Qt(this,!0)},has(o){return Jt.call(this,o,!0)},add:Ue("add"),set:Ue("set"),delete:Ue("delete"),clear:Ue("clear"),forEach:Zt(!0,!1)},r={get(o){return Yt(this,o,!0,!0)},get size(){return Qt(this,!0)},has(o){return Jt.call(this,o,!0)},add:Ue("add"),set:Ue("set"),delete:Ue("delete"),clear:Ue("clear"),forEach:Zt(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(o=>{e[o]=en(o,!1,!1),n[o]=en(o,!0,!1),t[o]=en(o,!1,!0),r[o]=en(o,!0,!0)}),[e,n,t,r]}const[Ji,Qi,Zi,el]=Yi();function Ir(e,t){const n=t?e?el:Zi:e?Qi:Ji;return(r,s,o)=>s==="__v_isReactive"?!e:s==="__v_isReadonly"?e:s==="__v_raw"?r:Reflect.get(Y(n,s)&&s in r?n:r,s,o)}const tl={get:Ir(!1,!1)},nl={get:Ir(!1,!0)},rl={get:Ir(!0,!1)};const fo=new WeakMap,ho=new WeakMap,po=new WeakMap,sl=new WeakMap;function ol(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function il(e){return e.__v_skip||!Object.isExtensible(e)?0:ol(Ri(e))}function Rn(e){return $t(e)?e:Mr(e,!1,Gi,tl,fo)}function ll(e){return Mr(e,!1,Xi,nl,ho)}function On(e){return Mr(e,!0,zi,rl,po)}function Mr(e,t,n,r,s){if(!Z(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=s.get(e);if(o)return o;const i=il(e);if(i===0)return e;const l=new Proxy(e,i===2?r:n);return s.set(e,l),l}function Rt(e){return $t(e)?Rt(e.__v_raw):!!(e&&e.__v_isReactive)}function $t(e){return!!(e&&e.__v_isReadonly)}function yn(e){return!!(e&&e.__v_isShallow)}function go(e){return e?!!e.__v_raw:!1}function J(e){const t=e&&e.__v_raw;return t?J(t):e}function dn(e){return Object.isExtensible(e)&&Qs(e,"__v_skip",!0),e}const Ht=e=>Z(e)?Rn(e):e,Pr=e=>Z(e)?On(e):e;class mo{constructor(t,n,r,s){this.getter=t,this._setter=n,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new Ar(()=>t(this._value),()=>Ot(this,this.effect._dirtyLevel===2?2:3)),this.effect.computed=this,this.effect.active=this._cacheable=!s,this.__v_isReadonly=r}get value(){const t=J(this);return(!t._cacheable||t.effect.dirty)&&Je(t._value,t._value=t.effect.run())&&Ot(t,4),Nr(t),t.effect._dirtyLevel>=2&&Ot(t,2),t._value}set value(t){this._setter(t)}get _dirty(){return this.effect.dirty}set _dirty(t){this.effect.dirty=t}}function cl(e,t,n=!1){let r,s;const o=K(e);return o?(r=e,s=xe):(r=e.get,s=e.set),new mo(r,s,o||!s,n)}function Nr(e){var t;Xe&&ct&&(e=J(e),oo(ct,(t=e.dep)!=null?t:e.dep=lo(()=>e.dep=void 0,e instanceof mo?e:void 0)))}function Ot(e,t=4,n){e=J(e);const r=e.dep;r&&io(r,t)}function de(e){return!!(e&&e.__v_isRef===!0)}function re(e){return yo(e,!1)}function Fr(e){return yo(e,!0)}function yo(e,t){return de(e)?e:new al(e,t)}class al{constructor(t,n){this.__v_isShallow=n,this.dep=void 0,this.__v_isRef=!0,this._rawValue=n?t:J(t),this._value=n?t:Ht(t)}get value(){return Nr(this),this._value}set value(t){const n=this.__v_isShallow||yn(t)||$t(t);t=n?t:J(t),Je(t,this._rawValue)&&(this._rawValue=t,this._value=n?t:Ht(t),Ot(this,4))}}function _o(e){return de(e)?e.value:e}const ul={get:(e,t,n)=>_o(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const s=e[t];return de(s)&&!de(n)?(s.value=n,!0):Reflect.set(e,t,n,r)}};function vo(e){return Rt(e)?e:new Proxy(e,ul)}class fl{constructor(t){this.dep=void 0,this.__v_isRef=!0;const{get:n,set:r}=t(()=>Nr(this),()=>Ot(this));this._get=n,this._set=r}get value(){return this._get()}set value(t){this._set(t)}}function dl(e){return new fl(e)}class hl{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0}get value(){const t=this._object[this._key];return t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Bi(J(this._object),this._key)}}class pl{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0}get value(){return this._getter()}}function gl(e,t,n){return de(e)?e:K(e)?new pl(e):Z(e)&&arguments.length>1?ml(e,t,n):re(e)}function ml(e,t,n){const r=e[t];return de(r)?r:new hl(e,t,n)}/**
* @vue/runtime-core v3.4.27
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Ye(e,t,n,r){try{return r?e(...r):e()}catch(s){Kt(s,t,n)}}function Se(e,t,n,r){if(K(e)){const s=Ye(e,t,n,r);return s&&Xs(s)&&s.catch(o=>{Kt(o,t,n)}),s}if(k(e)){const s=[];for(let o=0;o<e.length;o++)s.push(Se(e[o],t,n,r));return s}}function Kt(e,t,n,r=!0){const s=t?t.vnode:null;if(t){let o=t.parent;const i=t.proxy,l=`https://vuejs.org/error-reference/#runtime-${n}`;for(;o;){const a=o.ec;if(a){for(let f=0;f<a.length;f++)if(a[f](e,i,l)===!1)return}o=o.parent}const c=t.appContext.config.errorHandler;if(c){Ze(),Ye(c,null,10,[e,i,l]),et();return}}yl(e,n,s,r)}function yl(e,t,n,r=!0){console.error(e)}let jt=!1,fr=!1;const pe=[];let Pe=0;const vt=[];let We=null,ot=0;const bo=Promise.resolve();let $r=null;function Ln(e){const t=$r||bo;return e?t.then(this?e.bind(this):e):t}function _l(e){let t=Pe+1,n=pe.length;for(;t<n;){const r=t+n>>>1,s=pe[r],o=Vt(s);o<e||o===e&&s.pre?t=r+1:n=r}return t}function In(e){(!pe.length||!pe.includes(e,jt&&e.allowRecurse?Pe+1:Pe))&&(e.id==null?pe.push(e):pe.splice(_l(e.id),0,e),wo())}function wo(){!jt&&!fr&&(fr=!0,$r=bo.then(Eo))}function vl(e){const t=pe.indexOf(e);t>Pe&&pe.splice(t,1)}function bl(e){k(e)?vt.push(...e):(!We||!We.includes(e,e.allowRecurse?ot+1:ot))&&vt.push(e),wo()}function is(e,t,n=jt?Pe+1:0){for(;n<pe.length;n++){const r=pe[n];if(r&&r.pre){if(e&&r.id!==e.uid)continue;pe.splice(n,1),n--,r()}}}function _n(e){if(vt.length){const t=[...new Set(vt)].sort((n,r)=>Vt(n)-Vt(r));if(vt.length=0,We){We.push(...t);return}for(We=t,ot=0;ot<We.length;ot++)We[ot]();We=null,ot=0}}const Vt=e=>e.id==null?1/0:e.id,wl=(e,t)=>{const n=Vt(e)-Vt(t);if(n===0){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function Eo(e){fr=!1,jt=!0,pe.sort(wl);try{for(Pe=0;Pe<pe.length;Pe++){const t=pe[Pe];t&&t.active!==!1&&Ye(t,null,14)}}finally{Pe=0,pe.length=0,_n(),jt=!1,$r=null,(pe.length||vt.length)&&Eo()}}function El(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||ee;let s=n;const o=t.startsWith("update:"),i=o&&t.slice(7);if(i&&i in r){const f=`${i==="modelValue"?"model":i}Modifiers`,{number:h,trim:m}=r[f]||ee;m&&(s=n.map(v=>se(v)?v.trim():v)),h&&(s=n.map(lr))}let l,c=r[l=un(t)]||r[l=un($e(t))];!c&&o&&(c=r[l=un(dt(t))]),c&&Se(c,e,6,s);const a=r[l+"Once"];if(a){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Se(a,e,6,s)}}function Co(e,t,n=!1){const r=t.emitsCache,s=r.get(e);if(s!==void 0)return s;const o=e.emits;let i={},l=!1;if(!K(e)){const c=a=>{const f=Co(a,t,!0);f&&(l=!0,ie(i,f))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!o&&!l?(Z(e)&&r.set(e,null),null):(k(o)?o.forEach(c=>i[c]=null):ie(i,o),Z(e)&&r.set(e,i),i)}function Mn(e,t){return!e||!kt(t)?!1:(t=t.slice(2).replace(/Once$/,""),Y(e,t[0].toLowerCase()+t.slice(1))||Y(e,dt(t))||Y(e,t))}let ce=null,Pn=null;function vn(e){const t=ce;return ce=e,Pn=e&&e.type.__scopeId||null,t}function eu(e){Pn=e}function tu(){Pn=null}function Cl(e,t=ce,n){if(!t||e._n)return e;const r=(...s)=>{r._d&&bs(-1);const o=vn(t);let i;try{i=e(...s)}finally{vn(o),r._d&&bs(1)}return i};return r._n=!0,r._c=!0,r._d=!0,r}function kn(e){const{type:t,vnode:n,proxy:r,withProxy:s,propsOptions:[o],slots:i,attrs:l,emit:c,render:a,renderCache:f,props:h,data:m,setupState:v,ctx:C,inheritAttrs:I}=e,$=vn(e);let q,D;try{if(n.shapeFlag&4){const y=s||r,M=y;q=Ae(a.call(M,y,f,h,v,m,C)),D=l}else{const y=t;q=Ae(y.length>1?y(h,{attrs:l,slots:i,emit:c}):y(h,null)),D=t.props?l:xl(l)}}catch(y){Nt.length=0,Kt(y,e,1),q=oe(_e)}let p=q;if(D&&I!==!1){const y=Object.keys(D),{shapeFlag:M}=p;y.length&&M&7&&(o&&y.some(Er)&&(D=Sl(D,o)),p=Qe(p,D,!1,!0))}return n.dirs&&(p=Qe(p,null,!1,!0),p.dirs=p.dirs?p.dirs.concat(n.dirs):n.dirs),n.transition&&(p.transition=n.transition),q=p,vn($),q}const xl=e=>{let t;for(const n in e)(n==="class"||n==="style"||kt(n))&&((t||(t={}))[n]=e[n]);return t},Sl=(e,t)=>{const n={};for(const r in e)(!Er(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function Tl(e,t,n){const{props:r,children:s,component:o}=e,{props:i,children:l,patchFlag:c}=t,a=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return r?ls(r,i,a):!!i;if(c&8){const f=t.dynamicProps;for(let h=0;h<f.length;h++){const m=f[h];if(i[m]!==r[m]&&!Mn(a,m))return!0}}}else return(s||l)&&(!l||!l.$stable)?!0:r===i?!1:r?i?ls(r,i,a):!0:!!i;return!1}function ls(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let s=0;s<r.length;s++){const o=r[s];if(t[o]!==e[o]&&!Mn(n,o))return!0}return!1}function Al({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const Hr="components";function nu(e,t){return So(Hr,e,!0,t)||e}const xo=Symbol.for("v-ndc");function ru(e){return se(e)?So(Hr,e,!1)||e:e||xo}function So(e,t,n=!0,r=!1){const s=ce||ue;if(s){const o=s.type;if(e===Hr){const l=Sc(o,!1);if(l&&(l===t||l===$e(t)||l===Tn($e(t))))return o}const i=cs(s[e]||o[e],t)||cs(s.appContext[e],t);return!i&&r?o:i}}function cs(e,t){return e&&(e[t]||e[$e(t)]||e[Tn($e(t))])}const Rl=e=>e.__isSuspense;function To(e,t){t&&t.pendingBranch?k(e)?t.effects.push(...e):t.effects.push(e):bl(e)}const Ol=Symbol.for("v-scx"),Ll=()=>wt(Ol);function jr(e,t){return Nn(e,null,t)}function su(e,t){return Nn(e,null,{flush:"post"})}const tn={};function Ne(e,t,n){return Nn(e,t,n)}function Nn(e,t,{immediate:n,deep:r,flush:s,once:o,onTrack:i,onTrigger:l}=ee){if(t&&o){const O=t;t=(...N)=>{O(...N),M()}}const c=ue,a=O=>r===!0?O:lt(O,r===!1?1:void 0);let f,h=!1,m=!1;if(de(e)?(f=()=>e.value,h=yn(e)):Rt(e)?(f=()=>a(e),h=!0):k(e)?(m=!0,h=e.some(O=>Rt(O)||yn(O)),f=()=>e.map(O=>{if(de(O))return O.value;if(Rt(O))return a(O);if(K(O))return Ye(O,c,2)})):K(e)?t?f=()=>Ye(e,c,2):f=()=>(v&&v(),Se(e,c,3,[C])):f=xe,t&&r){const O=f;f=()=>lt(O())}let v,C=O=>{v=p.onStop=()=>{Ye(O,c,4),v=p.onStop=void 0}},I;if(Gt)if(C=xe,t?n&&Se(t,c,3,[f(),m?[]:void 0,C]):f(),s==="sync"){const O=Ll();I=O.__watcherHandles||(O.__watcherHandles=[])}else return xe;let $=m?new Array(e.length).fill(tn):tn;const q=()=>{if(!(!p.active||!p.dirty))if(t){const O=p.run();(r||h||(m?O.some((N,T)=>Je(N,$[T])):Je(O,$)))&&(v&&v(),Se(t,c,3,[O,$===tn?void 0:m&&$[0]===tn?[]:$,C]),$=O)}else p.run()};q.allowRecurse=!!t;let D;s==="sync"?D=q:s==="post"?D=()=>me(q,c&&c.suspense):(q.pre=!0,c&&(q.id=c.uid),D=()=>In(q));const p=new Ar(f,xe,D),y=no(),M=()=>{p.stop(),y&&Cr(y.effects,p)};return t?n?q():$=p.run():s==="post"?me(p.run.bind(p),c&&c.suspense):p.run(),I&&I.push(M),M}function Il(e,t,n){const r=this.proxy,s=se(e)?e.includes(".")?Ao(r,e):()=>r[e]:e.bind(r,r);let o;K(t)?o=t:(o=t.handler,n=t);const i=qt(this),l=Nn(s,o.bind(r),n);return i(),l}function Ao(e,t){const n=t.split(".");return()=>{let r=e;for(let s=0;s<n.length&&r;s++)r=r[n[s]];return r}}function lt(e,t=1/0,n){if(t<=0||!Z(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,de(e))lt(e.value,t,n);else if(k(e))for(let r=0;r<e.length;r++)lt(e[r],t,n);else if(zs(e)||yt(e))e.forEach(r=>{lt(r,t,n)});else if(Js(e))for(const r in e)lt(e[r],t,n);return e}function ou(e,t){if(ce===null)return e;const n=jn(ce)||ce.proxy,r=e.dirs||(e.dirs=[]);for(let s=0;s<t.length;s++){let[o,i,l,c=ee]=t[s];o&&(K(o)&&(o={mounted:o,updated:o}),o.deep&&lt(i),r.push({dir:o,instance:n,value:i,oldValue:void 0,arg:l,modifiers:c}))}return e}function Me(e,t,n,r){const s=e.dirs,o=t&&t.dirs;for(let i=0;i<s.length;i++){const l=s[i];o&&(l.oldValue=o[i].value);let c=l.dir[r];c&&(Ze(),Se(c,n,8,[e.el,l,e,t]),et())}}const qe=Symbol("_leaveCb"),nn=Symbol("_enterCb");function Ml(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return xt(()=>{e.isMounted=!0}),Mo(()=>{e.isUnmounting=!0}),e}const Ee=[Function,Array],Ro={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Ee,onEnter:Ee,onAfterEnter:Ee,onEnterCancelled:Ee,onBeforeLeave:Ee,onLeave:Ee,onAfterLeave:Ee,onLeaveCancelled:Ee,onBeforeAppear:Ee,onAppear:Ee,onAfterAppear:Ee,onAppearCancelled:Ee},Pl={name:"BaseTransition",props:Ro,setup(e,{slots:t}){const n=Hn(),r=Ml();return()=>{const s=t.default&&Lo(t.default(),!0);if(!s||!s.length)return;let o=s[0];if(s.length>1){for(const m of s)if(m.type!==_e){o=m;break}}const i=J(e),{mode:l}=i;if(r.isLeaving)return Kn(o);const c=as(o);if(!c)return Kn(o);const a=dr(c,i,r,n);hr(c,a);const f=n.subTree,h=f&&as(f);if(h&&h.type!==_e&&!it(c,h)){const m=dr(h,i,r,n);if(hr(h,m),l==="out-in"&&c.type!==_e)return r.isLeaving=!0,m.afterLeave=()=>{r.isLeaving=!1,n.update.active!==!1&&(n.effect.dirty=!0,n.update())},Kn(o);l==="in-out"&&c.type!==_e&&(m.delayLeave=(v,C,I)=>{const $=Oo(r,h);$[String(h.key)]=h,v[qe]=()=>{C(),v[qe]=void 0,delete a.delayedLeave},a.delayedLeave=I})}return o}}},Nl=Pl;function Oo(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function dr(e,t,n,r){const{appear:s,mode:o,persisted:i=!1,onBeforeEnter:l,onEnter:c,onAfterEnter:a,onEnterCancelled:f,onBeforeLeave:h,onLeave:m,onAfterLeave:v,onLeaveCancelled:C,onBeforeAppear:I,onAppear:$,onAfterAppear:q,onAppearCancelled:D}=t,p=String(e.key),y=Oo(n,e),M=(T,F)=>{T&&Se(T,r,9,F)},O=(T,F)=>{const w=F[1];M(T,F),k(T)?T.every(j=>j.length<=1)&&w():T.length<=1&&w()},N={mode:o,persisted:i,beforeEnter(T){let F=l;if(!n.isMounted)if(s)F=I||l;else return;T[qe]&&T[qe](!0);const w=y[p];w&&it(e,w)&&w.el[qe]&&w.el[qe](),M(F,[T])},enter(T){let F=c,w=a,j=f;if(!n.isMounted)if(s)F=$||c,w=q||a,j=D||f;else return;let A=!1;const G=T[nn]=le=>{A||(A=!0,le?M(j,[T]):M(w,[T]),N.delayedLeave&&N.delayedLeave(),T[nn]=void 0)};F?O(F,[T,G]):G()},leave(T,F){const w=String(e.key);if(T[nn]&&T[nn](!0),n.isUnmounting)return F();M(h,[T]);let j=!1;const A=T[qe]=G=>{j||(j=!0,F(),G?M(C,[T]):M(v,[T]),T[qe]=void 0,y[w]===e&&delete y[w])};y[w]=e,m?O(m,[T,A]):A()},clone(T){return dr(T,t,n,r)}};return N}function Kn(e){if(Wt(e))return e=Qe(e),e.children=null,e}function as(e){if(!Wt(e))return e;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&K(n.default))return n.default()}}function hr(e,t){e.shapeFlag&6&&e.component?hr(e.component.subTree,t):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Lo(e,t=!1,n){let r=[],s=0;for(let o=0;o<e.length;o++){let i=e[o];const l=n==null?i.key:String(n)+String(i.key!=null?i.key:o);i.type===ye?(i.patchFlag&128&&s++,r=r.concat(Lo(i.children,t,l))):(t||i.type!==_e)&&r.push(l!=null?Qe(i,{key:l}):i)}if(s>1)for(let o=0;o<r.length;o++)r[o].patchFlag=-2;return r}/*! #__NO_SIDE_EFFECTS__ */function Vr(e,t){return K(e)?ie({name:e.name},t,{setup:e}):e}const bt=e=>!!e.type.__asyncLoader;/*! #__NO_SIDE_EFFECTS__ */function iu(e){K(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:r,delay:s=200,timeout:o,suspensible:i=!0,onError:l}=e;let c=null,a,f=0;const h=()=>(f++,c=null,m()),m=()=>{let v;return c||(v=c=t().catch(C=>{if(C=C instanceof Error?C:new Error(String(C)),l)return new Promise((I,$)=>{l(C,()=>I(h()),()=>$(C),f+1)});throw C}).then(C=>v!==c&&c?c:(C&&(C.__esModule||C[Symbol.toStringTag]==="Module")&&(C=C.default),a=C,C)))};return Vr({name:"AsyncComponentWrapper",__asyncLoader:m,get __asyncResolved(){return a},setup(){const v=ue;if(a)return()=>Wn(a,v);const C=D=>{c=null,Kt(D,v,13,!r)};if(i&&v.suspense||Gt)return m().then(D=>()=>Wn(D,v)).catch(D=>(C(D),()=>r?oe(r,{error:D}):null));const I=re(!1),$=re(),q=re(!!s);return s&&setTimeout(()=>{q.value=!1},s),o!=null&&setTimeout(()=>{if(!I.value&&!$.value){const D=new Error(`Async component timed out after ${o}ms.`);C(D),$.value=D}},o),m().then(()=>{I.value=!0,v.parent&&Wt(v.parent.vnode)&&(v.parent.effect.dirty=!0,In(v.parent.update))}).catch(D=>{C(D),$.value=D}),()=>{if(I.value&&a)return Wn(a,v);if($.value&&r)return oe(r,{error:$.value});if(n&&!q.value)return oe(n)}}})}function Wn(e,t){const{ref:n,props:r,children:s,ce:o}=t.vnode,i=oe(e,r,s);return i.ref=n,i.ce=o,delete t.vnode.ce,i}const Wt=e=>e.type.__isKeepAlive;function Fl(e,t){Io(e,"a",t)}function $l(e,t){Io(e,"da",t)}function Io(e,t,n=ue){const r=e.__wdc||(e.__wdc=()=>{let s=n;for(;s;){if(s.isDeactivated)return;s=s.parent}return e()});if(Fn(t,r,n),n){let s=n.parent;for(;s&&s.parent;)Wt(s.parent.vnode)&&Hl(r,t,n,s),s=s.parent}}function Hl(e,t,n,r){const s=Fn(t,e,r,!0);$n(()=>{Cr(r[t],s)},n)}function Fn(e,t,n=ue,r=!1){if(n){const s=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{if(n.isUnmounted)return;Ze();const l=qt(n),c=Se(t,n,e,i);return l(),et(),c});return r?s.unshift(o):s.push(o),o}}const De=e=>(t,n=ue)=>(!Gt||e==="sp")&&Fn(e,(...r)=>t(...r),n),jl=De("bm"),xt=De("m"),Vl=De("bu"),Dl=De("u"),Mo=De("bum"),$n=De("um"),Ul=De("sp"),Bl=De("rtg"),kl=De("rtc");function Kl(e,t=ue){Fn("ec",e,t)}function lu(e,t,n,r){let s;const o=n;if(k(e)||se(e)){s=new Array(e.length);for(let i=0,l=e.length;i<l;i++)s[i]=t(e[i],i,void 0,o)}else if(typeof e=="number"){s=new Array(e);for(let i=0;i<e;i++)s[i]=t(i+1,i,void 0,o)}else if(Z(e))if(e[Symbol.iterator])s=Array.from(e,(i,l)=>t(i,l,void 0,o));else{const i=Object.keys(e);s=new Array(i.length);for(let l=0,c=i.length;l<c;l++){const a=i[l];s[l]=t(e[a],a,l,o)}}else s=[];return s}function cu(e,t,n={},r,s){if(ce.isCE||ce.parent&&bt(ce.parent)&&ce.parent.isCE)return t!=="default"&&(n.name=t),oe("slot",n,r&&r());let o=e[t];o&&o._c&&(o._d=!1),zo();const i=o&&Po(o(n)),l=Yo(ye,{key:n.key||i&&i.key||`_${t}`},i||(r?r():[]),i&&e._===1?64:-2);return!s&&l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),o&&o._c&&(o._d=!0),l}function Po(e){return e.some(t=>En(t)?!(t.type===_e||t.type===ye&&!Po(t.children)):!0)?e:null}function au(e,t){const n={};for(const r in e)n[/[A-Z]/.test(r)?`on:${r}`:un(r)]=e[r];return n}const pr=e=>e?ei(e)?jn(e)||e.proxy:pr(e.parent):null,Lt=ie(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>pr(e.parent),$root:e=>pr(e.root),$emit:e=>e.emit,$options:e=>Dr(e),$forceUpdate:e=>e.f||(e.f=()=>{e.effect.dirty=!0,In(e.update)}),$nextTick:e=>e.n||(e.n=Ln.bind(e.proxy)),$watch:e=>Il.bind(e)}),qn=(e,t)=>e!==ee&&!e.__isScriptSetup&&Y(e,t),Wl={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:s,props:o,accessCache:i,type:l,appContext:c}=e;let a;if(t[0]!=="$"){const v=i[t];if(v!==void 0)switch(v){case 1:return r[t];case 2:return s[t];case 4:return n[t];case 3:return o[t]}else{if(qn(r,t))return i[t]=1,r[t];if(s!==ee&&Y(s,t))return i[t]=2,s[t];if((a=e.propsOptions[0])&&Y(a,t))return i[t]=3,o[t];if(n!==ee&&Y(n,t))return i[t]=4,n[t];gr&&(i[t]=0)}}const f=Lt[t];let h,m;if(f)return t==="$attrs"&&ve(e.attrs,"get",""),f(e);if((h=l.__cssModules)&&(h=h[t]))return h;if(n!==ee&&Y(n,t))return i[t]=4,n[t];if(m=c.config.globalProperties,Y(m,t))return m[t]},set({_:e},t,n){const{data:r,setupState:s,ctx:o}=e;return qn(s,t)?(s[t]=n,!0):r!==ee&&Y(r,t)?(r[t]=n,!0):Y(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:s,propsOptions:o}},i){let l;return!!n[i]||e!==ee&&Y(e,i)||qn(t,i)||(l=o[0])&&Y(l,i)||Y(r,i)||Y(Lt,i)||Y(s.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:Y(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function uu(){return ql().slots}function ql(){const e=Hn();return e.setupContext||(e.setupContext=ni(e))}function us(e){return k(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let gr=!0;function Gl(e){const t=Dr(e),n=e.proxy,r=e.ctx;gr=!1,t.beforeCreate&&fs(t.beforeCreate,e,"bc");const{data:s,computed:o,methods:i,watch:l,provide:c,inject:a,created:f,beforeMount:h,mounted:m,beforeUpdate:v,updated:C,activated:I,deactivated:$,beforeDestroy:q,beforeUnmount:D,destroyed:p,unmounted:y,render:M,renderTracked:O,renderTriggered:N,errorCaptured:T,serverPrefetch:F,expose:w,inheritAttrs:j,components:A,directives:G,filters:le}=t;if(a&&zl(a,r,null),i)for(const X in i){const V=i[X];K(V)&&(r[X]=V.bind(n))}if(s){const X=s.call(n,n);Z(X)&&(e.data=Rn(X))}if(gr=!0,o)for(const X in o){const V=o[X],He=K(V)?V.bind(n,n):K(V.get)?V.get.bind(n,n):xe,zt=!K(V)&&K(V.set)?V.set.bind(n):xe,tt=ne({get:He,set:zt});Object.defineProperty(r,X,{enumerable:!0,configurable:!0,get:()=>tt.value,set:Le=>tt.value=Le})}if(l)for(const X in l)No(l[X],r,n,X);if(c){const X=K(c)?c.call(n):c;Reflect.ownKeys(X).forEach(V=>{ec(V,X[V])})}f&&fs(f,e,"c");function U(X,V){k(V)?V.forEach(He=>X(He.bind(n))):V&&X(V.bind(n))}if(U(jl,h),U(xt,m),U(Vl,v),U(Dl,C),U(Fl,I),U($l,$),U(Kl,T),U(kl,O),U(Bl,N),U(Mo,D),U($n,y),U(Ul,F),k(w))if(w.length){const X=e.exposed||(e.exposed={});w.forEach(V=>{Object.defineProperty(X,V,{get:()=>n[V],set:He=>n[V]=He})})}else e.exposed||(e.exposed={});M&&e.render===xe&&(e.render=M),j!=null&&(e.inheritAttrs=j),A&&(e.components=A),G&&(e.directives=G)}function zl(e,t,n=xe){k(e)&&(e=mr(e));for(const r in e){const s=e[r];let o;Z(s)?"default"in s?o=wt(s.from||r,s.default,!0):o=wt(s.from||r):o=wt(s),de(o)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[r]=o}}function fs(e,t,n){Se(k(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function No(e,t,n,r){const s=r.includes(".")?Ao(n,r):()=>n[r];if(se(e)){const o=t[e];K(o)&&Ne(s,o)}else if(K(e))Ne(s,e.bind(n));else if(Z(e))if(k(e))e.forEach(o=>No(o,t,n,r));else{const o=K(e.handler)?e.handler.bind(n):t[e.handler];K(o)&&Ne(s,o,e)}}function Dr(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:s,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let c;return l?c=l:!s.length&&!n&&!r?c=t:(c={},s.length&&s.forEach(a=>bn(c,a,i,!0)),bn(c,t,i)),Z(t)&&o.set(t,c),c}function bn(e,t,n,r=!1){const{mixins:s,extends:o}=t;o&&bn(e,o,n,!0),s&&s.forEach(i=>bn(e,i,n,!0));for(const i in t)if(!(r&&i==="expose")){const l=Xl[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const Xl={data:ds,props:hs,emits:hs,methods:At,computed:At,beforeCreate:ge,created:ge,beforeMount:ge,mounted:ge,beforeUpdate:ge,updated:ge,beforeDestroy:ge,beforeUnmount:ge,destroyed:ge,unmounted:ge,activated:ge,deactivated:ge,errorCaptured:ge,serverPrefetch:ge,components:At,directives:At,watch:Jl,provide:ds,inject:Yl};function ds(e,t){return t?e?function(){return ie(K(e)?e.call(this,this):e,K(t)?t.call(this,this):t)}:t:e}function Yl(e,t){return At(mr(e),mr(t))}function mr(e){if(k(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ge(e,t){return e?[...new Set([].concat(e,t))]:t}function At(e,t){return e?ie(Object.create(null),e,t):t}function hs(e,t){return e?k(e)&&k(t)?[...new Set([...e,...t])]:ie(Object.create(null),us(e),us(t??{})):t}function Jl(e,t){if(!e)return t;if(!t)return e;const n=ie(Object.create(null),e);for(const r in t)n[r]=ge(e[r],t[r]);return n}function Fo(){return{app:null,config:{isNativeTag:Ti,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Ql=0;function Zl(e,t){return function(r,s=null){K(r)||(r=ie({},r)),s!=null&&!Z(s)&&(s=null);const o=Fo(),i=new WeakSet;let l=!1;const c=o.app={_uid:Ql++,_component:r,_props:s,_container:null,_context:o,_instance:null,version:Ac,get config(){return o.config},set config(a){},use(a,...f){return i.has(a)||(a&&K(a.install)?(i.add(a),a.install(c,...f)):K(a)&&(i.add(a),a(c,...f))),c},mixin(a){return o.mixins.includes(a)||o.mixins.push(a),c},component(a,f){return f?(o.components[a]=f,c):o.components[a]},directive(a,f){return f?(o.directives[a]=f,c):o.directives[a]},mount(a,f,h){if(!l){const m=oe(r,s);return m.appContext=o,h===!0?h="svg":h===!1&&(h=void 0),f&&t?t(m,a):e(m,a,h),l=!0,c._container=a,a.__vue_app__=c,jn(m.component)||m.component.proxy}},unmount(){l&&(e(null,c._container),delete c._container.__vue_app__)},provide(a,f){return o.provides[a]=f,c},runWithContext(a){const f=It;It=c;try{return a()}finally{It=f}}};return c}}let It=null;function ec(e,t){if(ue){let n=ue.provides;const r=ue.parent&&ue.parent.provides;r===n&&(n=ue.provides=Object.create(r)),n[e]=t}}function wt(e,t,n=!1){const r=ue||ce;if(r||It){const s=r?r.parent==null?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:It._context.provides;if(s&&e in s)return s[e];if(arguments.length>1)return n&&K(t)?t.call(r&&r.proxy):t}}const $o={},Ho=()=>Object.create($o),jo=e=>Object.getPrototypeOf(e)===$o;function tc(e,t,n,r=!1){const s={},o=Ho();e.propsDefaults=Object.create(null),Vo(e,t,s,o);for(const i in e.propsOptions[0])i in s||(s[i]=void 0);n?e.props=r?s:ll(s):e.type.props?e.props=s:e.props=o,e.attrs=o}function nc(e,t,n,r){const{props:s,attrs:o,vnode:{patchFlag:i}}=e,l=J(s),[c]=e.propsOptions;let a=!1;if((r||i>0)&&!(i&16)){if(i&8){const f=e.vnode.dynamicProps;for(let h=0;h<f.length;h++){let m=f[h];if(Mn(e.emitsOptions,m))continue;const v=t[m];if(c)if(Y(o,m))v!==o[m]&&(o[m]=v,a=!0);else{const C=$e(m);s[C]=yr(c,l,C,v,e,!1)}else v!==o[m]&&(o[m]=v,a=!0)}}}else{Vo(e,t,s,o)&&(a=!0);let f;for(const h in l)(!t||!Y(t,h)&&((f=dt(h))===h||!Y(t,f)))&&(c?n&&(n[h]!==void 0||n[f]!==void 0)&&(s[h]=yr(c,l,h,void 0,e,!0)):delete s[h]);if(o!==l)for(const h in o)(!t||!Y(t,h))&&(delete o[h],a=!0)}a&&Ve(e.attrs,"set","")}function Vo(e,t,n,r){const[s,o]=e.propsOptions;let i=!1,l;if(t)for(let c in t){if(_t(c))continue;const a=t[c];let f;s&&Y(s,f=$e(c))?!o||!o.includes(f)?n[f]=a:(l||(l={}))[f]=a:Mn(e.emitsOptions,c)||(!(c in r)||a!==r[c])&&(r[c]=a,i=!0)}if(o){const c=J(n),a=l||ee;for(let f=0;f<o.length;f++){const h=o[f];n[h]=yr(s,c,h,a[h],e,!Y(a,h))}}return i}function yr(e,t,n,r,s,o){const i=e[n];if(i!=null){const l=Y(i,"default");if(l&&r===void 0){const c=i.default;if(i.type!==Function&&!i.skipFactory&&K(c)){const{propsDefaults:a}=s;if(n in a)r=a[n];else{const f=qt(s);r=a[n]=c.call(null,t),f()}}else r=c}i[0]&&(o&&!l?r=!1:i[1]&&(r===""||r===dt(n))&&(r=!0))}return r}function Do(e,t,n=!1){const r=t.propsCache,s=r.get(e);if(s)return s;const o=e.props,i={},l=[];let c=!1;if(!K(e)){const f=h=>{c=!0;const[m,v]=Do(h,t,!0);ie(i,m),v&&l.push(...v)};!n&&t.mixins.length&&t.mixins.forEach(f),e.extends&&f(e.extends),e.mixins&&e.mixins.forEach(f)}if(!o&&!c)return Z(e)&&r.set(e,mt),mt;if(k(o))for(let f=0;f<o.length;f++){const h=$e(o[f]);ps(h)&&(i[h]=ee)}else if(o)for(const f in o){const h=$e(f);if(ps(h)){const m=o[f],v=i[h]=k(m)||K(m)?{type:m}:ie({},m);if(v){const C=ys(Boolean,v.type),I=ys(String,v.type);v[0]=C>-1,v[1]=I<0||C<I,(C>-1||Y(v,"default"))&&l.push(h)}}}const a=[i,l];return Z(e)&&r.set(e,a),a}function ps(e){return e[0]!=="$"&&!_t(e)}function gs(e){return e===null?"null":typeof e=="function"?e.name||"":typeof e=="object"&&e.constructor&&e.constructor.name||""}function ms(e,t){return gs(e)===gs(t)}function ys(e,t){return k(t)?t.findIndex(n=>ms(n,e)):K(t)&&ms(t,e)?0:-1}const Uo=e=>e[0]==="_"||e==="$stable",Ur=e=>k(e)?e.map(Ae):[Ae(e)],rc=(e,t,n)=>{if(t._n)return t;const r=Cl((...s)=>Ur(t(...s)),n);return r._c=!1,r},Bo=(e,t,n)=>{const r=e._ctx;for(const s in e){if(Uo(s))continue;const o=e[s];if(K(o))t[s]=rc(s,o,r);else if(o!=null){const i=Ur(o);t[s]=()=>i}}},ko=(e,t)=>{const n=Ur(t);e.slots.default=()=>n},sc=(e,t)=>{const n=e.slots=Ho();if(e.vnode.shapeFlag&32){const r=t._;r?(ie(n,t),Qs(n,"_",r,!0)):Bo(t,n)}else t&&ko(e,t)},oc=(e,t,n)=>{const{vnode:r,slots:s}=e;let o=!0,i=ee;if(r.shapeFlag&32){const l=t._;l?n&&l===1?o=!1:(ie(s,t),!n&&l===1&&delete s._):(o=!t.$stable,Bo(t,s)),i=t}else t&&(ko(e,t),i={default:1});if(o)for(const l in s)!Uo(l)&&i[l]==null&&delete s[l]};function wn(e,t,n,r,s=!1){if(k(e)){e.forEach((m,v)=>wn(m,t&&(k(t)?t[v]:t),n,r,s));return}if(bt(r)&&!s)return;const o=r.shapeFlag&4?jn(r.component)||r.component.proxy:r.el,i=s?null:o,{i:l,r:c}=e,a=t&&t.r,f=l.refs===ee?l.refs={}:l.refs,h=l.setupState;if(a!=null&&a!==c&&(se(a)?(f[a]=null,Y(h,a)&&(h[a]=null)):de(a)&&(a.value=null)),K(c))Ye(c,l,12,[i,f]);else{const m=se(c),v=de(c);if(m||v){const C=()=>{if(e.f){const I=m?Y(h,c)?h[c]:f[c]:c.value;s?k(I)&&Cr(I,o):k(I)?I.includes(o)||I.push(o):m?(f[c]=[o],Y(h,c)&&(h[c]=f[c])):(c.value=[o],e.k&&(f[e.k]=c.value))}else m?(f[c]=i,Y(h,c)&&(h[c]=i)):v&&(c.value=i,e.k&&(f[e.k]=i))};i?(C.id=-1,me(C,n)):C()}}}let Be=!1;const ic=e=>e.namespaceURI.includes("svg")&&e.tagName!=="foreignObject",lc=e=>e.namespaceURI.includes("MathML"),rn=e=>{if(ic(e))return"svg";if(lc(e))return"mathml"},sn=e=>e.nodeType===8;function cc(e){const{mt:t,p:n,o:{patchProp:r,createText:s,nextSibling:o,parentNode:i,remove:l,insert:c,createComment:a}}=e,f=(p,y)=>{if(!y.hasChildNodes()){n(null,p,y),_n(),y._vnode=p;return}Be=!1,h(y.firstChild,p,null,null,null),_n(),y._vnode=p,Be&&console.error("Hydration completed but contains mismatches.")},h=(p,y,M,O,N,T=!1)=>{T=T||!!y.dynamicChildren;const F=sn(p)&&p.data==="[",w=()=>I(p,y,M,O,N,F),{type:j,ref:A,shapeFlag:G,patchFlag:le}=y;let fe=p.nodeType;y.el=p,le===-2&&(T=!1,y.dynamicChildren=null);let U=null;switch(j){case Et:fe!==3?y.children===""?(c(y.el=s(""),i(p),p),U=p):U=w():(p.data!==y.children&&(Be=!0,p.data=y.children),U=o(p));break;case _e:D(p)?(U=o(p),q(y.el=p.content.firstChild,p,M)):fe!==8||F?U=w():U=o(p);break;case Pt:if(F&&(p=o(p),fe=p.nodeType),fe===1||fe===3){U=p;const X=!y.children.length;for(let V=0;V<y.staticCount;V++)X&&(y.children+=U.nodeType===1?U.outerHTML:U.data),V===y.staticCount-1&&(y.anchor=U),U=o(U);return F?o(U):U}else w();break;case ye:F?U=C(p,y,M,O,N,T):U=w();break;default:if(G&1)(fe!==1||y.type.toLowerCase()!==p.tagName.toLowerCase())&&!D(p)?U=w():U=m(p,y,M,O,N,T);else if(G&6){y.slotScopeIds=N;const X=i(p);if(F?U=$(p):sn(p)&&p.data==="teleport start"?U=$(p,p.data,"teleport end"):U=o(p),t(y,X,null,M,O,rn(X),T),bt(y)){let V;F?(V=oe(ye),V.anchor=U?U.previousSibling:X.lastChild):V=p.nodeType===3?Zo(""):oe("div"),V.el=p,y.component.subTree=V}}else G&64?fe!==8?U=w():U=y.type.hydrate(p,y,M,O,N,T,e,v):G&128&&(U=y.type.hydrate(p,y,M,O,rn(i(p)),N,T,e,h))}return A!=null&&wn(A,null,O,y),U},m=(p,y,M,O,N,T)=>{T=T||!!y.dynamicChildren;const{type:F,props:w,patchFlag:j,shapeFlag:A,dirs:G,transition:le}=y,fe=F==="input"||F==="option";if(fe||j!==-1){G&&Me(y,null,M,"created");let U=!1;if(D(p)){U=Wo(O,le)&&M&&M.vnode.props&&M.vnode.props.appear;const V=p.content.firstChild;U&&le.beforeEnter(V),q(V,p,M),y.el=p=V}if(A&16&&!(w&&(w.innerHTML||w.textContent))){let V=v(p.firstChild,y,p,M,O,N,T);for(;V;){Be=!0;const He=V;V=V.nextSibling,l(He)}}else A&8&&p.textContent!==y.children&&(Be=!0,p.textContent=y.children);if(w)if(fe||!T||j&48)for(const V in w)(fe&&(V.endsWith("value")||V==="indeterminate")||kt(V)&&!_t(V)||V[0]===".")&&r(p,V,null,w[V],void 0,void 0,M);else w.onClick&&r(p,"onClick",null,w.onClick,void 0,void 0,M);let X;(X=w&&w.onVnodeBeforeMount)&&Ce(X,M,y),G&&Me(y,null,M,"beforeMount"),((X=w&&w.onVnodeMounted)||G||U)&&To(()=>{X&&Ce(X,M,y),U&&le.enter(p),G&&Me(y,null,M,"mounted")},O)}return p.nextSibling},v=(p,y,M,O,N,T,F)=>{F=F||!!y.dynamicChildren;const w=y.children,j=w.length;for(let A=0;A<j;A++){const G=F?w[A]:w[A]=Ae(w[A]);if(p)p=h(p,G,O,N,T,F);else{if(G.type===Et&&!G.children)continue;Be=!0,n(null,G,M,null,O,N,rn(M),T)}}return p},C=(p,y,M,O,N,T)=>{const{slotScopeIds:F}=y;F&&(N=N?N.concat(F):F);const w=i(p),j=v(o(p),y,w,M,O,N,T);return j&&sn(j)&&j.data==="]"?o(y.anchor=j):(Be=!0,c(y.anchor=a("]"),w,j),j)},I=(p,y,M,O,N,T)=>{if(Be=!0,y.el=null,T){const j=$(p);for(;;){const A=o(p);if(A&&A!==j)l(A);else break}}const F=o(p),w=i(p);return l(p),n(null,y,w,F,M,O,rn(w),N),F},$=(p,y="[",M="]")=>{let O=0;for(;p;)if(p=o(p),p&&sn(p)&&(p.data===y&&O++,p.data===M)){if(O===0)return o(p);O--}return p},q=(p,y,M)=>{const O=y.parentNode;O&&O.replaceChild(p,y);let N=M;for(;N;)N.vnode.el===y&&(N.vnode.el=N.subTree.el=p),N=N.parent},D=p=>p.nodeType===1&&p.tagName.toLowerCase()==="template";return[f,h]}const me=To;function ac(e){return Ko(e)}function uc(e){return Ko(e,cc)}function Ko(e,t){const n=Zs();n.__VUE__=!0;const{insert:r,remove:s,patchProp:o,createElement:i,createText:l,createComment:c,setText:a,setElementText:f,parentNode:h,nextSibling:m,setScopeId:v=xe,insertStaticContent:C}=e,I=(u,d,g,_=null,b=null,S=null,L=void 0,x=null,R=!!d.dynamicChildren)=>{if(u===d)return;u&&!it(u,d)&&(_=Xt(u),Le(u,b,S,!0),u=null),d.patchFlag===-2&&(R=!1,d.dynamicChildren=null);const{type:E,ref:P,shapeFlag:B}=d;switch(E){case Et:$(u,d,g,_);break;case _e:q(u,d,g,_);break;case Pt:u==null&&D(d,g,_,L);break;case ye:A(u,d,g,_,b,S,L,x,R);break;default:B&1?M(u,d,g,_,b,S,L,x,R):B&6?G(u,d,g,_,b,S,L,x,R):(B&64||B&128)&&E.process(u,d,g,_,b,S,L,x,R,ht)}P!=null&&b&&wn(P,u&&u.ref,S,d||u,!d)},$=(u,d,g,_)=>{if(u==null)r(d.el=l(d.children),g,_);else{const b=d.el=u.el;d.children!==u.children&&a(b,d.children)}},q=(u,d,g,_)=>{u==null?r(d.el=c(d.children||""),g,_):d.el=u.el},D=(u,d,g,_)=>{[u.el,u.anchor]=C(u.children,d,g,_,u.el,u.anchor)},p=({el:u,anchor:d},g,_)=>{let b;for(;u&&u!==d;)b=m(u),r(u,g,_),u=b;r(d,g,_)},y=({el:u,anchor:d})=>{let g;for(;u&&u!==d;)g=m(u),s(u),u=g;s(d)},M=(u,d,g,_,b,S,L,x,R)=>{d.type==="svg"?L="svg":d.type==="math"&&(L="mathml"),u==null?O(d,g,_,b,S,L,x,R):F(u,d,b,S,L,x,R)},O=(u,d,g,_,b,S,L,x)=>{let R,E;const{props:P,shapeFlag:B,transition:H,dirs:W}=u;if(R=u.el=i(u.type,S,P&&P.is,P),B&8?f(R,u.children):B&16&&T(u.children,R,null,_,b,Gn(u,S),L,x),W&&Me(u,null,_,"created"),N(R,u,u.scopeId,L,_),P){for(const Q in P)Q!=="value"&&!_t(Q)&&o(R,Q,null,P[Q],S,u.children,_,b,je);"value"in P&&o(R,"value",null,P.value,S),(E=P.onVnodeBeforeMount)&&Ce(E,_,u)}W&&Me(u,null,_,"beforeMount");const z=Wo(b,H);z&&H.beforeEnter(R),r(R,d,g),((E=P&&P.onVnodeMounted)||z||W)&&me(()=>{E&&Ce(E,_,u),z&&H.enter(R),W&&Me(u,null,_,"mounted")},b)},N=(u,d,g,_,b)=>{if(g&&v(u,g),_)for(let S=0;S<_.length;S++)v(u,_[S]);if(b){let S=b.subTree;if(d===S){const L=b.vnode;N(u,L,L.scopeId,L.slotScopeIds,b.parent)}}},T=(u,d,g,_,b,S,L,x,R=0)=>{for(let E=R;E<u.length;E++){const P=u[E]=x?Ge(u[E]):Ae(u[E]);I(null,P,d,g,_,b,S,L,x)}},F=(u,d,g,_,b,S,L)=>{const x=d.el=u.el;let{patchFlag:R,dynamicChildren:E,dirs:P}=d;R|=u.patchFlag&16;const B=u.props||ee,H=d.props||ee;let W;if(g&&nt(g,!1),(W=H.onVnodeBeforeUpdate)&&Ce(W,g,d,u),P&&Me(d,u,g,"beforeUpdate"),g&&nt(g,!0),E?w(u.dynamicChildren,E,x,g,_,Gn(d,b),S):L||V(u,d,x,null,g,_,Gn(d,b),S,!1),R>0){if(R&16)j(x,d,B,H,g,_,b);else if(R&2&&B.class!==H.class&&o(x,"class",null,H.class,b),R&4&&o(x,"style",B.style,H.style,b),R&8){const z=d.dynamicProps;for(let Q=0;Q<z.length;Q++){const te=z[Q],ae=B[te],Te=H[te];(Te!==ae||te==="value")&&o(x,te,ae,Te,b,u.children,g,_,je)}}R&1&&u.children!==d.children&&f(x,d.children)}else!L&&E==null&&j(x,d,B,H,g,_,b);((W=H.onVnodeUpdated)||P)&&me(()=>{W&&Ce(W,g,d,u),P&&Me(d,u,g,"updated")},_)},w=(u,d,g,_,b,S,L)=>{for(let x=0;x<d.length;x++){const R=u[x],E=d[x],P=R.el&&(R.type===ye||!it(R,E)||R.shapeFlag&70)?h(R.el):g;I(R,E,P,null,_,b,S,L,!0)}},j=(u,d,g,_,b,S,L)=>{if(g!==_){if(g!==ee)for(const x in g)!_t(x)&&!(x in _)&&o(u,x,g[x],null,L,d.children,b,S,je);for(const x in _){if(_t(x))continue;const R=_[x],E=g[x];R!==E&&x!=="value"&&o(u,x,E,R,L,d.children,b,S,je)}"value"in _&&o(u,"value",g.value,_.value,L)}},A=(u,d,g,_,b,S,L,x,R)=>{const E=d.el=u?u.el:l(""),P=d.anchor=u?u.anchor:l("");let{patchFlag:B,dynamicChildren:H,slotScopeIds:W}=d;W&&(x=x?x.concat(W):W),u==null?(r(E,g,_),r(P,g,_),T(d.children||[],g,P,b,S,L,x,R)):B>0&&B&64&&H&&u.dynamicChildren?(w(u.dynamicChildren,H,g,b,S,L,x),(d.key!=null||b&&d===b.subTree)&&Br(u,d,!0)):V(u,d,g,P,b,S,L,x,R)},G=(u,d,g,_,b,S,L,x,R)=>{d.slotScopeIds=x,u==null?d.shapeFlag&512?b.ctx.activate(d,g,_,L,R):le(d,g,_,b,S,L,R):fe(u,d,R)},le=(u,d,g,_,b,S,L)=>{const x=u.component=wc(u,_,b);if(Wt(u)&&(x.ctx.renderer=ht),Ec(x),x.asyncDep){if(b&&b.registerDep(x,U),!u.el){const R=x.subTree=oe(_e);q(null,R,d,g)}}else U(x,u,d,g,b,S,L)},fe=(u,d,g)=>{const _=d.component=u.component;if(Tl(u,d,g))if(_.asyncDep&&!_.asyncResolved){X(_,d,g);return}else _.next=d,vl(_.update),_.effect.dirty=!0,_.update();else d.el=u.el,_.vnode=d},U=(u,d,g,_,b,S,L)=>{const x=()=>{if(u.isMounted){let{next:P,bu:B,u:H,parent:W,vnode:z}=u;{const pt=qo(u);if(pt){P&&(P.el=z.el,X(u,P,L)),pt.asyncDep.then(()=>{u.isUnmounted||x()});return}}let Q=P,te;nt(u,!1),P?(P.el=z.el,X(u,P,L)):P=z,B&&fn(B),(te=P.props&&P.props.onVnodeBeforeUpdate)&&Ce(te,W,P,z),nt(u,!0);const ae=kn(u),Te=u.subTree;u.subTree=ae,I(Te,ae,h(Te.el),Xt(Te),u,b,S),P.el=ae.el,Q===null&&Al(u,ae.el),H&&me(H,b),(te=P.props&&P.props.onVnodeUpdated)&&me(()=>Ce(te,W,P,z),b)}else{let P;const{el:B,props:H}=d,{bm:W,m:z,parent:Q}=u,te=bt(d);if(nt(u,!1),W&&fn(W),!te&&(P=H&&H.onVnodeBeforeMount)&&Ce(P,Q,d),nt(u,!0),B&&Un){const ae=()=>{u.subTree=kn(u),Un(B,u.subTree,u,b,null)};te?d.type.__asyncLoader().then(()=>!u.isUnmounted&&ae()):ae()}else{const ae=u.subTree=kn(u);I(null,ae,g,_,u,b,S),d.el=ae.el}if(z&&me(z,b),!te&&(P=H&&H.onVnodeMounted)){const ae=d;me(()=>Ce(P,Q,ae),b)}(d.shapeFlag&256||Q&&bt(Q.vnode)&&Q.vnode.shapeFlag&256)&&u.a&&me(u.a,b),u.isMounted=!0,d=g=_=null}},R=u.effect=new Ar(x,xe,()=>In(E),u.scope),E=u.update=()=>{R.dirty&&R.run()};E.id=u.uid,nt(u,!0),E()},X=(u,d,g)=>{d.component=u;const _=u.vnode.props;u.vnode=d,u.next=null,nc(u,d.props,_,g),oc(u,d.children,g),Ze(),is(u),et()},V=(u,d,g,_,b,S,L,x,R=!1)=>{const E=u&&u.children,P=u?u.shapeFlag:0,B=d.children,{patchFlag:H,shapeFlag:W}=d;if(H>0){if(H&128){zt(E,B,g,_,b,S,L,x,R);return}else if(H&256){He(E,B,g,_,b,S,L,x,R);return}}W&8?(P&16&&je(E,b,S),B!==E&&f(g,B)):P&16?W&16?zt(E,B,g,_,b,S,L,x,R):je(E,b,S,!0):(P&8&&f(g,""),W&16&&T(B,g,_,b,S,L,x,R))},He=(u,d,g,_,b,S,L,x,R)=>{u=u||mt,d=d||mt;const E=u.length,P=d.length,B=Math.min(E,P);let H;for(H=0;H<B;H++){const W=d[H]=R?Ge(d[H]):Ae(d[H]);I(u[H],W,g,null,b,S,L,x,R)}E>P?je(u,b,S,!0,!1,B):T(d,g,_,b,S,L,x,R,B)},zt=(u,d,g,_,b,S,L,x,R)=>{let E=0;const P=d.length;let B=u.length-1,H=P-1;for(;E<=B&&E<=H;){const W=u[E],z=d[E]=R?Ge(d[E]):Ae(d[E]);if(it(W,z))I(W,z,g,null,b,S,L,x,R);else break;E++}for(;E<=B&&E<=H;){const W=u[B],z=d[H]=R?Ge(d[H]):Ae(d[H]);if(it(W,z))I(W,z,g,null,b,S,L,x,R);else break;B--,H--}if(E>B){if(E<=H){const W=H+1,z=W<P?d[W].el:_;for(;E<=H;)I(null,d[E]=R?Ge(d[E]):Ae(d[E]),g,z,b,S,L,x,R),E++}}else if(E>H)for(;E<=B;)Le(u[E],b,S,!0),E++;else{const W=E,z=E,Q=new Map;for(E=z;E<=H;E++){const be=d[E]=R?Ge(d[E]):Ae(d[E]);be.key!=null&&Q.set(be.key,E)}let te,ae=0;const Te=H-z+1;let pt=!1,Xr=0;const St=new Array(Te);for(E=0;E<Te;E++)St[E]=0;for(E=W;E<=B;E++){const be=u[E];if(ae>=Te){Le(be,b,S,!0);continue}let Ie;if(be.key!=null)Ie=Q.get(be.key);else for(te=z;te<=H;te++)if(St[te-z]===0&&it(be,d[te])){Ie=te;break}Ie===void 0?Le(be,b,S,!0):(St[Ie-z]=E+1,Ie>=Xr?Xr=Ie:pt=!0,I(be,d[Ie],g,null,b,S,L,x,R),ae++)}const Yr=pt?fc(St):mt;for(te=Yr.length-1,E=Te-1;E>=0;E--){const be=z+E,Ie=d[be],Jr=be+1<P?d[be+1].el:_;St[E]===0?I(null,Ie,g,Jr,b,S,L,x,R):pt&&(te<0||E!==Yr[te]?tt(Ie,g,Jr,2):te--)}}},tt=(u,d,g,_,b=null)=>{const{el:S,type:L,transition:x,children:R,shapeFlag:E}=u;if(E&6){tt(u.component.subTree,d,g,_);return}if(E&128){u.suspense.move(d,g,_);return}if(E&64){L.move(u,d,g,ht);return}if(L===ye){r(S,d,g);for(let B=0;B<R.length;B++)tt(R[B],d,g,_);r(u.anchor,d,g);return}if(L===Pt){p(u,d,g);return}if(_!==2&&E&1&&x)if(_===0)x.beforeEnter(S),r(S,d,g),me(()=>x.enter(S),b);else{const{leave:B,delayLeave:H,afterLeave:W}=x,z=()=>r(S,d,g),Q=()=>{B(S,()=>{z(),W&&W()})};H?H(S,z,Q):Q()}else r(S,d,g)},Le=(u,d,g,_=!1,b=!1)=>{const{type:S,props:L,ref:x,children:R,dynamicChildren:E,shapeFlag:P,patchFlag:B,dirs:H}=u;if(x!=null&&wn(x,null,g,u,!0),P&256){d.ctx.deactivate(u);return}const W=P&1&&H,z=!bt(u);let Q;if(z&&(Q=L&&L.onVnodeBeforeUnmount)&&Ce(Q,d,u),P&6)Si(u.component,g,_);else{if(P&128){u.suspense.unmount(g,_);return}W&&Me(u,null,d,"beforeUnmount"),P&64?u.type.remove(u,d,g,b,ht,_):E&&(S!==ye||B>0&&B&64)?je(E,d,g,!1,!0):(S===ye&&B&384||!b&&P&16)&&je(R,d,g),_&&Gr(u)}(z&&(Q=L&&L.onVnodeUnmounted)||W)&&me(()=>{Q&&Ce(Q,d,u),W&&Me(u,null,d,"unmounted")},g)},Gr=u=>{const{type:d,el:g,anchor:_,transition:b}=u;if(d===ye){xi(g,_);return}if(d===Pt){y(u);return}const S=()=>{s(g),b&&!b.persisted&&b.afterLeave&&b.afterLeave()};if(u.shapeFlag&1&&b&&!b.persisted){const{leave:L,delayLeave:x}=b,R=()=>L(g,S);x?x(u.el,S,R):R()}else S()},xi=(u,d)=>{let g;for(;u!==d;)g=m(u),s(u),u=g;s(d)},Si=(u,d,g)=>{const{bum:_,scope:b,update:S,subTree:L,um:x}=u;_&&fn(_),b.stop(),S&&(S.active=!1,Le(L,u,d,g)),x&&me(x,d),me(()=>{u.isUnmounted=!0},d),d&&d.pendingBranch&&!d.isUnmounted&&u.asyncDep&&!u.asyncResolved&&u.suspenseId===d.pendingId&&(d.deps--,d.deps===0&&d.resolve())},je=(u,d,g,_=!1,b=!1,S=0)=>{for(let L=S;L<u.length;L++)Le(u[L],d,g,_,b)},Xt=u=>u.shapeFlag&6?Xt(u.component.subTree):u.shapeFlag&128?u.suspense.next():m(u.anchor||u.el);let Vn=!1;const zr=(u,d,g)=>{u==null?d._vnode&&Le(d._vnode,null,null,!0):I(d._vnode||null,u,d,null,null,null,g),Vn||(Vn=!0,is(),_n(),Vn=!1),d._vnode=u},ht={p:I,um:Le,m:tt,r:Gr,mt:le,mc:T,pc:V,pbc:w,n:Xt,o:e};let Dn,Un;return t&&([Dn,Un]=t(ht)),{render:zr,hydrate:Dn,createApp:Zl(zr,Dn)}}function Gn({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function nt({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Wo(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Br(e,t,n=!1){const r=e.children,s=t.children;if(k(r)&&k(s))for(let o=0;o<r.length;o++){const i=r[o];let l=s[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=s[o]=Ge(s[o]),l.el=i.el),n||Br(i,l)),l.type===Et&&(l.el=i.el)}}function fc(e){const t=e.slice(),n=[0];let r,s,o,i,l;const c=e.length;for(r=0;r<c;r++){const a=e[r];if(a!==0){if(s=n[n.length-1],e[s]<a){t[r]=s,n.push(r);continue}for(o=0,i=n.length-1;o<i;)l=o+i>>1,e[n[l]]<a?o=l+1:i=l;a<e[n[o]]&&(o>0&&(t[r]=n[o-1]),n[o]=r)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function qo(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:qo(t)}const dc=e=>e.__isTeleport,Mt=e=>e&&(e.disabled||e.disabled===""),_s=e=>typeof SVGElement<"u"&&e instanceof SVGElement,vs=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,_r=(e,t)=>{const n=e&&e.to;return se(n)?t?t(n):null:n},hc={name:"Teleport",__isTeleport:!0,process(e,t,n,r,s,o,i,l,c,a){const{mc:f,pc:h,pbc:m,o:{insert:v,querySelector:C,createText:I,createComment:$}}=a,q=Mt(t.props);let{shapeFlag:D,children:p,dynamicChildren:y}=t;if(e==null){const M=t.el=I(""),O=t.anchor=I("");v(M,n,r),v(O,n,r);const N=t.target=_r(t.props,C),T=t.targetAnchor=I("");N&&(v(T,N),i==="svg"||_s(N)?i="svg":(i==="mathml"||vs(N))&&(i="mathml"));const F=(w,j)=>{D&16&&f(p,w,j,s,o,i,l,c)};q?F(n,O):N&&F(N,T)}else{t.el=e.el;const M=t.anchor=e.anchor,O=t.target=e.target,N=t.targetAnchor=e.targetAnchor,T=Mt(e.props),F=T?n:O,w=T?M:N;if(i==="svg"||_s(O)?i="svg":(i==="mathml"||vs(O))&&(i="mathml"),y?(m(e.dynamicChildren,y,F,s,o,i,l),Br(e,t,!0)):c||h(e,t,F,w,s,o,i,l,!1),q)T?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):on(t,n,M,a,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const j=t.target=_r(t.props,C);j&&on(t,j,null,a,0)}else T&&on(t,O,N,a,1)}Go(t)},remove(e,t,n,r,{um:s,o:{remove:o}},i){const{shapeFlag:l,children:c,anchor:a,targetAnchor:f,target:h,props:m}=e;if(h&&o(f),i&&o(a),l&16){const v=i||!Mt(m);for(let C=0;C<c.length;C++){const I=c[C];s(I,t,n,v,!!I.dynamicChildren)}}},move:on,hydrate:pc};function on(e,t,n,{o:{insert:r},m:s},o=2){o===0&&r(e.targetAnchor,t,n);const{el:i,anchor:l,shapeFlag:c,children:a,props:f}=e,h=o===2;if(h&&r(i,t,n),(!h||Mt(f))&&c&16)for(let m=0;m<a.length;m++)s(a[m],t,n,2);h&&r(l,t,n)}function pc(e,t,n,r,s,o,{o:{nextSibling:i,parentNode:l,querySelector:c}},a){const f=t.target=_r(t.props,c);if(f){const h=f._lpa||f.firstChild;if(t.shapeFlag&16)if(Mt(t.props))t.anchor=a(i(e),t,l(e),n,r,s,o),t.targetAnchor=h;else{t.anchor=i(e);let m=h;for(;m;)if(m=i(m),m&&m.nodeType===8&&m.data==="teleport anchor"){t.targetAnchor=m,f._lpa=t.targetAnchor&&i(t.targetAnchor);break}a(h,t,f,n,r,s,o)}Go(t)}return t.anchor&&i(t.anchor)}const fu=hc;function Go(e){const t=e.ctx;if(t&&t.ut){let n=e.children[0].el;for(;n&&n!==e.targetAnchor;)n.nodeType===1&&n.setAttribute("data-v-owner",t.uid),n=n.nextSibling;t.ut()}}const ye=Symbol.for("v-fgt"),Et=Symbol.for("v-txt"),_e=Symbol.for("v-cmt"),Pt=Symbol.for("v-stc"),Nt=[];let Re=null;function zo(e=!1){Nt.push(Re=e?null:[])}function gc(){Nt.pop(),Re=Nt[Nt.length-1]||null}let Dt=1;function bs(e){Dt+=e}function Xo(e){return e.dynamicChildren=Dt>0?Re||mt:null,gc(),Dt>0&&Re&&Re.push(e),e}function du(e,t,n,r,s,o){return Xo(Qo(e,t,n,r,s,o,!0))}function Yo(e,t,n,r,s){return Xo(oe(e,t,n,r,s,!0))}function En(e){return e?e.__v_isVNode===!0:!1}function it(e,t){return e.type===t.type&&e.key===t.key}const Jo=({key:e})=>e??null,hn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?se(e)||de(e)||K(e)?{i:ce,r:e,k:t,f:!!n}:e:null);function Qo(e,t=null,n=null,r=0,s=null,o=e===ye?0:1,i=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Jo(t),ref:t&&hn(t),scopeId:Pn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:r,dynamicProps:s,dynamicChildren:null,appContext:null,ctx:ce};return l?(kr(c,n),o&128&&e.normalize(c)):n&&(c.shapeFlag|=se(n)?8:16),Dt>0&&!i&&Re&&(c.patchFlag>0||o&6)&&c.patchFlag!==32&&Re.push(c),c}const oe=mc;function mc(e,t=null,n=null,r=0,s=null,o=!1){if((!e||e===xo)&&(e=_e),En(e)){const l=Qe(e,t,!0);return n&&kr(l,n),Dt>0&&!o&&Re&&(l.shapeFlag&6?Re[Re.indexOf(e)]=l:Re.push(l)),l.patchFlag|=-2,l}if(Tc(e)&&(e=e.__vccOpts),t){t=yc(t);let{class:l,style:c}=t;l&&!se(l)&&(t.class=Tr(l)),Z(c)&&(go(c)&&!k(c)&&(c=ie({},c)),t.style=Sr(c))}const i=se(e)?1:Rl(e)?128:dc(e)?64:Z(e)?4:K(e)?2:0;return Qo(e,t,n,r,s,i,o,!0)}function yc(e){return e?go(e)||jo(e)?ie({},e):e:null}function Qe(e,t,n=!1,r=!1){const{props:s,ref:o,patchFlag:i,children:l,transition:c}=e,a=t?_c(s||{},t):s,f={__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&Jo(a),ref:t&&t.ref?n&&o?k(o)?o.concat(hn(t)):[o,hn(t)]:hn(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ye?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Qe(e.ssContent),ssFallback:e.ssFallback&&Qe(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&r&&(f.transition=c.clone(f)),f}function Zo(e=" ",t=0){return oe(Et,null,e,t)}function hu(e,t){const n=oe(Pt,null,e);return n.staticCount=t,n}function pu(e="",t=!1){return t?(zo(),Yo(_e,null,e)):oe(_e,null,e)}function Ae(e){return e==null||typeof e=="boolean"?oe(_e):k(e)?oe(ye,null,e.slice()):typeof e=="object"?Ge(e):oe(Et,null,String(e))}function Ge(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Qe(e)}function kr(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(k(t))n=16;else if(typeof t=="object")if(r&65){const s=t.default;s&&(s._c&&(s._d=!1),kr(e,s()),s._c&&(s._d=!0));return}else{n=32;const s=t._;!s&&!jo(t)?t._ctx=ce:s===3&&ce&&(ce.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else K(t)?(t={default:t,_ctx:ce},n=32):(t=String(t),r&64?(n=16,t=[Zo(t)]):n=8);e.children=t,e.shapeFlag|=n}function _c(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const s in r)if(s==="class")t.class!==r.class&&(t.class=Tr([t.class,r.class]));else if(s==="style")t.style=Sr([t.style,r.style]);else if(kt(s)){const o=t[s],i=r[s];i&&o!==i&&!(k(o)&&o.includes(i))&&(t[s]=o?[].concat(o,i):i)}else s!==""&&(t[s]=r[s])}return t}function Ce(e,t,n,r=null){Se(e,t,7,[n,r])}const vc=Fo();let bc=0;function wc(e,t,n){const r=e.type,s=(t?t.appContext:e.appContext)||vc,o={uid:bc++,vnode:e,type:r,parent:t,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,scope:new ji(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(s.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Do(r,s),emitsOptions:Co(r,s),emit:null,emitted:null,propsDefaults:ee,inheritAttrs:r.inheritAttrs,ctx:ee,data:ee,props:ee,attrs:ee,slots:ee,refs:ee,setupState:ee,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=El.bind(null,o),e.ce&&e.ce(o),o}let ue=null;const Hn=()=>ue||ce;let Cn,vr;{const e=Zs(),t=(n,r)=>{let s;return(s=e[n])||(s=e[n]=[]),s.push(r),o=>{s.length>1?s.forEach(i=>i(o)):s[0](o)}};Cn=t("__VUE_INSTANCE_SETTERS__",n=>ue=n),vr=t("__VUE_SSR_SETTERS__",n=>Gt=n)}const qt=e=>{const t=ue;return Cn(e),e.scope.on(),()=>{e.scope.off(),Cn(t)}},ws=()=>{ue&&ue.scope.off(),Cn(null)};function ei(e){return e.vnode.shapeFlag&4}let Gt=!1;function Ec(e,t=!1){t&&vr(t);const{props:n,children:r}=e.vnode,s=ei(e);tc(e,n,s,t),sc(e,r);const o=s?Cc(e,t):void 0;return t&&vr(!1),o}function Cc(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Wl);const{setup:r}=n;if(r){const s=e.setupContext=r.length>1?ni(e):null,o=qt(e);Ze();const i=Ye(r,e,0,[e.props,s]);if(et(),o(),Xs(i)){if(i.then(ws,ws),t)return i.then(l=>{Es(e,l,t)}).catch(l=>{Kt(l,e,0)});e.asyncDep=i}else Es(e,i,t)}else ti(e,t)}function Es(e,t,n){K(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Z(t)&&(e.setupState=vo(t)),ti(e,n)}let Cs;function ti(e,t,n){const r=e.type;if(!e.render){if(!t&&Cs&&!r.render){const s=r.template||Dr(e).template;if(s){const{isCustomElement:o,compilerOptions:i}=e.appContext.config,{delimiters:l,compilerOptions:c}=r,a=ie(ie({isCustomElement:o,delimiters:l},i),c);r.render=Cs(s,a)}}e.render=r.render||xe}{const s=qt(e);Ze();try{Gl(e)}finally{et(),s()}}}const xc={get(e,t){return ve(e,"get",""),e[t]}};function ni(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,xc),slots:e.slots,emit:e.emit,expose:t}}function jn(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(vo(dn(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Lt)return Lt[n](e)},has(t,n){return n in t||n in Lt}}))}function Sc(e,t=!0){return K(e)?e.displayName||e.name:e.name||t&&e.__name}function Tc(e){return K(e)&&"__vccOpts"in e}const ne=(e,t)=>cl(e,t,Gt);function br(e,t,n){const r=arguments.length;return r===2?Z(t)&&!k(t)?En(t)?oe(e,null,[t]):oe(e,t):oe(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&En(n)&&(n=[n]),oe(e,t,n))}const Ac="3.4.27";/**
* @vue/runtime-dom v3.4.27
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const Rc="http://www.w3.org/2000/svg",Oc="http://www.w3.org/1998/Math/MathML",ze=typeof document<"u"?document:null,xs=ze&&ze.createElement("template"),Lc={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const s=t==="svg"?ze.createElementNS(Rc,e):t==="mathml"?ze.createElementNS(Oc,e):ze.createElement(e,n?{is:n}:void 0);return e==="select"&&r&&r.multiple!=null&&s.setAttribute("multiple",r.multiple),s},createText:e=>ze.createTextNode(e),createComment:e=>ze.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>ze.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,s,o){const i=n?n.previousSibling:t.lastChild;if(s&&(s===o||s.nextSibling))for(;t.insertBefore(s.cloneNode(!0),n),!(s===o||!(s=s.nextSibling)););else{xs.innerHTML=r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e;const l=xs.content;if(r==="svg"||r==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},ke="transition",Tt="animation",Ut=Symbol("_vtc"),ri=(e,{slots:t})=>br(Nl,Ic(e),t);ri.displayName="Transition";const si={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};ri.props=ie({},Ro,si);const rt=(e,t=[])=>{k(e)?e.forEach(n=>n(...t)):e&&e(...t)},Ss=e=>e?k(e)?e.some(t=>t.length>1):e.length>1:!1;function Ic(e){const t={};for(const A in e)A in si||(t[A]=e[A]);if(e.css===!1)return t;const{name:n="v",type:r,duration:s,enterFromClass:o=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:l=`${n}-enter-to`,appearFromClass:c=o,appearActiveClass:a=i,appearToClass:f=l,leaveFromClass:h=`${n}-leave-from`,leaveActiveClass:m=`${n}-leave-active`,leaveToClass:v=`${n}-leave-to`}=e,C=Mc(s),I=C&&C[0],$=C&&C[1],{onBeforeEnter:q,onEnter:D,onEnterCancelled:p,onLeave:y,onLeaveCancelled:M,onBeforeAppear:O=q,onAppear:N=D,onAppearCancelled:T=p}=t,F=(A,G,le)=>{st(A,G?f:l),st(A,G?a:i),le&&le()},w=(A,G)=>{A._isLeaving=!1,st(A,h),st(A,v),st(A,m),G&&G()},j=A=>(G,le)=>{const fe=A?N:D,U=()=>F(G,A,le);rt(fe,[G,U]),Ts(()=>{st(G,A?c:o),Ke(G,A?f:l),Ss(fe)||As(G,r,I,U)})};return ie(t,{onBeforeEnter(A){rt(q,[A]),Ke(A,o),Ke(A,i)},onBeforeAppear(A){rt(O,[A]),Ke(A,c),Ke(A,a)},onEnter:j(!1),onAppear:j(!0),onLeave(A,G){A._isLeaving=!0;const le=()=>w(A,G);Ke(A,h),Ke(A,m),Fc(),Ts(()=>{A._isLeaving&&(st(A,h),Ke(A,v),Ss(y)||As(A,r,$,le))}),rt(y,[A,le])},onEnterCancelled(A){F(A,!1),rt(p,[A])},onAppearCancelled(A){F(A,!0),rt(T,[A])},onLeaveCancelled(A){w(A),rt(M,[A])}})}function Mc(e){if(e==null)return null;if(Z(e))return[zn(e.enter),zn(e.leave)];{const t=zn(e);return[t,t]}}function zn(e){return Ii(e)}function Ke(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[Ut]||(e[Ut]=new Set)).add(t)}function st(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.remove(r));const n=e[Ut];n&&(n.delete(t),n.size||(e[Ut]=void 0))}function Ts(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Pc=0;function As(e,t,n,r){const s=e._endId=++Pc,o=()=>{s===e._endId&&r()};if(n)return setTimeout(o,n);const{type:i,timeout:l,propCount:c}=Nc(e,t);if(!i)return r();const a=i+"end";let f=0;const h=()=>{e.removeEventListener(a,m),o()},m=v=>{v.target===e&&++f>=c&&h()};setTimeout(()=>{f<c&&h()},l+1),e.addEventListener(a,m)}function Nc(e,t){const n=window.getComputedStyle(e),r=C=>(n[C]||"").split(", "),s=r(`${ke}Delay`),o=r(`${ke}Duration`),i=Rs(s,o),l=r(`${Tt}Delay`),c=r(`${Tt}Duration`),a=Rs(l,c);let f=null,h=0,m=0;t===ke?i>0&&(f=ke,h=i,m=o.length):t===Tt?a>0&&(f=Tt,h=a,m=c.length):(h=Math.max(i,a),f=h>0?i>a?ke:Tt:null,m=f?f===ke?o.length:c.length:0);const v=f===ke&&/\b(transform|all)(,|$)/.test(r(`${ke}Property`).toString());return{type:f,timeout:h,propCount:m,hasTransform:v}}function Rs(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,r)=>Os(n)+Os(e[r])))}function Os(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function Fc(){return document.body.offsetHeight}function $c(e,t,n){const r=e[Ut];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Ls=Symbol("_vod"),Hc=Symbol("_vsh"),jc=Symbol(""),Vc=/(^|;)\s*display\s*:/;function Dc(e,t,n){const r=e.style,s=se(n);let o=!1;if(n&&!s){if(t)if(se(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&pn(r,l,"")}else for(const i in t)n[i]==null&&pn(r,i,"");for(const i in n)i==="display"&&(o=!0),pn(r,i,n[i])}else if(s){if(t!==n){const i=r[jc];i&&(n+=";"+i),r.cssText=n,o=Vc.test(n)}}else t&&e.removeAttribute("style");Ls in e&&(e[Ls]=o?r.display:"",e[Hc]&&(r.display="none"))}const Is=/\s*!important$/;function pn(e,t,n){if(k(n))n.forEach(r=>pn(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=Uc(e,t);Is.test(n)?e.setProperty(dt(r),n.replace(Is,""),"important"):e[r]=n}}const Ms=["Webkit","Moz","ms"],Xn={};function Uc(e,t){const n=Xn[t];if(n)return n;let r=$e(t);if(r!=="filter"&&r in e)return Xn[t]=r;r=Tn(r);for(let s=0;s<Ms.length;s++){const o=Ms[s]+r;if(o in e)return Xn[t]=o}return t}const Ps="http://www.w3.org/1999/xlink";function Bc(e,t,n,r,s){if(r&&t.startsWith("xlink:"))n==null?e.removeAttributeNS(Ps,t.slice(6,t.length)):e.setAttributeNS(Ps,t,n);else{const o=Hi(t);n==null||o&&!eo(n)?e.removeAttribute(t):e.setAttribute(t,o?"":n)}}function kc(e,t,n,r,s,o,i){if(t==="innerHTML"||t==="textContent"){r&&i(r,s,o),e[t]=n??"";return}const l=e.tagName;if(t==="value"&&l!=="PROGRESS"&&!l.includes("-")){const a=l==="OPTION"?e.getAttribute("value")||"":e.value,f=n??"";(a!==f||!("_value"in e))&&(e.value=f),n==null&&e.removeAttribute(t),e._value=n;return}let c=!1;if(n===""||n==null){const a=typeof e[t];a==="boolean"?n=eo(n):n==null&&a==="string"?(n="",c=!0):a==="number"&&(n=0,c=!0)}try{e[t]=n}catch{}c&&e.removeAttribute(t)}function gt(e,t,n,r){e.addEventListener(t,n,r)}function Kc(e,t,n,r){e.removeEventListener(t,n,r)}const Ns=Symbol("_vei");function Wc(e,t,n,r,s=null){const o=e[Ns]||(e[Ns]={}),i=o[t];if(r&&i)i.value=r;else{const[l,c]=qc(t);if(r){const a=o[t]=Xc(r,s);gt(e,l,a,c)}else i&&(Kc(e,l,i,c),o[t]=void 0)}}const Fs=/(?:Once|Passive|Capture)$/;function qc(e){let t;if(Fs.test(e)){t={};let r;for(;r=e.match(Fs);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):dt(e.slice(2)),t]}let Yn=0;const Gc=Promise.resolve(),zc=()=>Yn||(Gc.then(()=>Yn=0),Yn=Date.now());function Xc(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;Se(Yc(r,n.value),t,5,[r])};return n.value=e,n.attached=zc(),n}function Yc(e,t){if(k(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>s=>!s._stopped&&r&&r(s))}else return t}const $s=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Jc=(e,t,n,r,s,o,i,l,c)=>{const a=s==="svg";t==="class"?$c(e,r,a):t==="style"?Dc(e,n,r):kt(t)?Er(t)||Wc(e,t,n,r,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Qc(e,t,r,a))?kc(e,t,r,o,i,l,c):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),Bc(e,t,r,a))};function Qc(e,t,n,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&$s(t)&&K(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const s=e.tagName;if(s==="IMG"||s==="VIDEO"||s==="CANVAS"||s==="SOURCE")return!1}return $s(t)&&se(n)?!1:t in e}const Hs=e=>{const t=e.props["onUpdate:modelValue"]||!1;return k(t)?n=>fn(t,n):t};function Zc(e){e.target.composing=!0}function js(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Jn=Symbol("_assign"),gu={created(e,{modifiers:{lazy:t,trim:n,number:r}},s){e[Jn]=Hs(s);const o=r||s.props&&s.props.type==="number";gt(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;n&&(l=l.trim()),o&&(l=lr(l)),e[Jn](l)}),n&&gt(e,"change",()=>{e.value=e.value.trim()}),t||(gt(e,"compositionstart",Zc),gt(e,"compositionend",js),gt(e,"change",js))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,modifiers:{lazy:n,trim:r,number:s}},o){if(e[Jn]=Hs(o),e.composing)return;const i=(s||e.type==="number")&&!/^0\d/.test(e.value)?lr(e.value):e.value,l=t??"";i!==l&&(document.activeElement===e&&e.type!=="range"&&(n||r&&e.value.trim()===l)||(e.value=l))}},ea=["ctrl","shift","alt","meta"],ta={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>ea.some(n=>e[`${n}Key`]&&!t.includes(n))},mu=(e,t)=>{const n=e._withMods||(e._withMods={}),r=t.join(".");return n[r]||(n[r]=(s,...o)=>{for(let i=0;i<t.length;i++){const l=ta[t[i]];if(l&&l(s,t))return}return e(s,...o)})},na={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},yu=(e,t)=>{const n=e._withKeys||(e._withKeys={}),r=t.join(".");return n[r]||(n[r]=s=>{if(!("key"in s))return;const o=dt(s.key);if(t.some(i=>i===o||na[i]===o))return e(s)})},oi=ie({patchProp:Jc},Lc);let Ft,Vs=!1;function ra(){return Ft||(Ft=ac(oi))}function sa(){return Ft=Vs?Ft:uc(oi),Vs=!0,Ft}const _u=(...e)=>{const t=ra().createApp(...e),{mount:n}=t;return t.mount=r=>{const s=li(r);if(!s)return;const o=t._component;!K(o)&&!o.render&&!o.template&&(o.template=s.innerHTML),s.innerHTML="";const i=n(s,!1,ii(s));return s instanceof Element&&(s.removeAttribute("v-cloak"),s.setAttribute("data-v-app","")),i},t},vu=(...e)=>{const t=sa().createApp(...e),{mount:n}=t;return t.mount=r=>{const s=li(r);if(s)return n(s,!0,ii(s))},t};function ii(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function li(e){return se(e)?document.querySelector(e):e}const bu=(e,t)=>{const n=e.__vccOpts||e;for(const[r,s]of t)n[r]=s;return n},oa="modulepreload",ia=function(e){return"/"+e},Ds={},wu=function(t,n,r){let s=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const o=document.querySelector("meta[property=csp-nonce]"),i=(o==null?void 0:o.nonce)||(o==null?void 0:o.getAttribute("nonce"));s=Promise.all(n.map(l=>{if(l=ia(l),l in Ds)return;Ds[l]=!0;const c=l.endsWith(".css"),a=c?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${l}"]${a}`))return;const f=document.createElement("link");if(f.rel=c?"stylesheet":oa,c||(f.as="script",f.crossOrigin=""),f.href=l,i&&f.setAttribute("nonce",i),document.head.appendChild(f),c)return new Promise((h,m)=>{f.addEventListener("load",h),f.addEventListener("error",()=>m(new Error(`Unable to preload CSS for ${l}`)))})}))}return s.then(()=>t()).catch(o=>{const i=new Event("vite:preloadError",{cancelable:!0});if(i.payload=o,window.dispatchEvent(i),!i.defaultPrevented)throw o})},la=window.__VP_SITE_DATA__;function Kr(e){return no()?(Di(e),!0):!1}function Fe(e){return typeof e=="function"?e():_o(e)}const ci=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const ca=Object.prototype.toString,aa=e=>ca.call(e)==="[object Object]",Bt=()=>{},Us=ua();function ua(){var e,t;return ci&&((e=window==null?void 0:window.navigator)==null?void 0:e.userAgent)&&(/iP(?:ad|hone|od)/.test(window.navigator.userAgent)||((t=window==null?void 0:window.navigator)==null?void 0:t.maxTouchPoints)>2&&/iPad|Macintosh/.test(window==null?void 0:window.navigator.userAgent))}function fa(e,t){function n(...r){return new Promise((s,o)=>{Promise.resolve(e(()=>t.apply(this,r),{fn:t,thisArg:this,args:r})).then(s).catch(o)})}return n}const ai=e=>e();function da(e,t={}){let n,r,s=Bt;const o=l=>{clearTimeout(l),s(),s=Bt};return l=>{const c=Fe(e),a=Fe(t.maxWait);return n&&o(n),c<=0||a!==void 0&&a<=0?(r&&(o(r),r=null),Promise.resolve(l())):new Promise((f,h)=>{s=t.rejectOnCancel?h:f,a&&!r&&(r=setTimeout(()=>{n&&o(n),r=null,f(l())},a)),n=setTimeout(()=>{r&&o(r),r=null,f(l())},c)})}}function ha(e=ai){const t=re(!0);function n(){t.value=!1}function r(){t.value=!0}const s=(...o)=>{t.value&&e(...o)};return{isActive:On(t),pause:n,resume:r,eventFilter:s}}function pa(e){return Hn()}function ui(...e){if(e.length!==1)return gl(...e);const t=e[0];return typeof t=="function"?On(dl(()=>({get:t,set:Bt}))):re(t)}function fi(e,t,n={}){const{eventFilter:r=ai,...s}=n;return Ne(e,fa(r,t),s)}function ga(e,t,n={}){const{eventFilter:r,...s}=n,{eventFilter:o,pause:i,resume:l,isActive:c}=ha(r);return{stop:fi(e,t,{...s,eventFilter:o}),pause:i,resume:l,isActive:c}}function Wr(e,t=!0,n){pa()?xt(e,n):t?e():Ln(e)}function Eu(e,t,n={}){const{debounce:r=0,maxWait:s=void 0,...o}=n;return fi(e,t,{...o,eventFilter:da(r,{maxWait:s})})}function Cu(e,t,n){let r;de(n)?r={evaluating:n}:r={};const{lazy:s=!1,evaluating:o=void 0,shallow:i=!0,onError:l=Bt}=r,c=re(!s),a=i?Fr(t):re(t);let f=0;return jr(async h=>{if(!c.value)return;f++;const m=f;let v=!1;o&&Promise.resolve().then(()=>{o.value=!0});try{const C=await e(I=>{h(()=>{o&&(o.value=!1),v||I()})});m===f&&(a.value=C)}catch(C){l(C)}finally{o&&m===f&&(o.value=!1),v=!0}}),s?ne(()=>(c.value=!0,a.value)):a}function di(e){var t;const n=Fe(e);return(t=n==null?void 0:n.$el)!=null?t:n}const Oe=ci?window:void 0;function Ct(...e){let t,n,r,s;if(typeof e[0]=="string"||Array.isArray(e[0])?([n,r,s]=e,t=Oe):[t,n,r,s]=e,!t)return Bt;Array.isArray(n)||(n=[n]),Array.isArray(r)||(r=[r]);const o=[],i=()=>{o.forEach(f=>f()),o.length=0},l=(f,h,m,v)=>(f.addEventListener(h,m,v),()=>f.removeEventListener(h,m,v)),c=Ne(()=>[di(t),Fe(s)],([f,h])=>{if(i(),!f)return;const m=aa(h)?{...h}:h;o.push(...n.flatMap(v=>r.map(C=>l(f,v,C,m))))},{immediate:!0,flush:"post"}),a=()=>{c(),i()};return Kr(a),a}function ma(e){return typeof e=="function"?e:typeof e=="string"?t=>t.key===e:Array.isArray(e)?t=>e.includes(t.key):()=>!0}function xu(...e){let t,n,r={};e.length===3?(t=e[0],n=e[1],r=e[2]):e.length===2?typeof e[1]=="object"?(t=!0,n=e[0],r=e[1]):(t=e[0],n=e[1]):(t=!0,n=e[0]);const{target:s=Oe,eventName:o="keydown",passive:i=!1,dedupe:l=!1}=r,c=ma(t);return Ct(s,o,f=>{f.repeat&&Fe(l)||c(f)&&n(f)},i)}function ya(){const e=re(!1),t=Hn();return t&&xt(()=>{e.value=!0},t),e}function _a(e){const t=ya();return ne(()=>(t.value,!!e()))}function hi(e,t={}){const{window:n=Oe}=t,r=_a(()=>n&&"matchMedia"in n&&typeof n.matchMedia=="function");let s;const o=re(!1),i=a=>{o.value=a.matches},l=()=>{s&&("removeEventListener"in s?s.removeEventListener("change",i):s.removeListener(i))},c=jr(()=>{r.value&&(l(),s=n.matchMedia(Fe(e)),"addEventListener"in s?s.addEventListener("change",i):s.addListener(i),o.value=s.matches)});return Kr(()=>{c(),l(),s=void 0}),o}const ln=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},cn="__vueuse_ssr_handlers__",va=ba();function ba(){return cn in ln||(ln[cn]=ln[cn]||{}),ln[cn]}function pi(e,t){return va[e]||t}function wa(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}const Ea={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},Bs="vueuse-storage";function qr(e,t,n,r={}){var s;const{flush:o="pre",deep:i=!0,listenToStorageChanges:l=!0,writeDefaults:c=!0,mergeDefaults:a=!1,shallow:f,window:h=Oe,eventFilter:m,onError:v=w=>{console.error(w)},initOnMounted:C}=r,I=(f?Fr:re)(typeof t=="function"?t():t);if(!n)try{n=pi("getDefaultStorage",()=>{var w;return(w=Oe)==null?void 0:w.localStorage})()}catch(w){v(w)}if(!n)return I;const $=Fe(t),q=wa($),D=(s=r.serializer)!=null?s:Ea[q],{pause:p,resume:y}=ga(I,()=>O(I.value),{flush:o,deep:i,eventFilter:m});h&&l&&Wr(()=>{Ct(h,"storage",T),Ct(h,Bs,F),C&&T()}),C||T();function M(w,j){h&&h.dispatchEvent(new CustomEvent(Bs,{detail:{key:e,oldValue:w,newValue:j,storageArea:n}}))}function O(w){try{const j=n.getItem(e);if(w==null)M(j,null),n.removeItem(e);else{const A=D.write(w);j!==A&&(n.setItem(e,A),M(j,A))}}catch(j){v(j)}}function N(w){const j=w?w.newValue:n.getItem(e);if(j==null)return c&&$!=null&&n.setItem(e,D.write($)),$;if(!w&&a){const A=D.read(j);return typeof a=="function"?a(A,$):q==="object"&&!Array.isArray(A)?{...$,...A}:A}else return typeof j!="string"?j:D.read(j)}function T(w){if(!(w&&w.storageArea!==n)){if(w&&w.key==null){I.value=$;return}if(!(w&&w.key!==e)){p();try{(w==null?void 0:w.newValue)!==D.write(I.value)&&(I.value=N(w))}catch(j){v(j)}finally{w?Ln(y):y()}}}}function F(w){T(w.detail)}return I}function gi(e){return hi("(prefers-color-scheme: dark)",e)}function Ca(e={}){const{selector:t="html",attribute:n="class",initialValue:r="auto",window:s=Oe,storage:o,storageKey:i="vueuse-color-scheme",listenToStorageChanges:l=!0,storageRef:c,emitAuto:a,disableTransition:f=!0}=e,h={auto:"",light:"light",dark:"dark",...e.modes||{}},m=gi({window:s}),v=ne(()=>m.value?"dark":"light"),C=c||(i==null?ui(r):qr(i,r,o,{window:s,listenToStorageChanges:l})),I=ne(()=>C.value==="auto"?v.value:C.value),$=pi("updateHTMLAttrs",(y,M,O)=>{const N=typeof y=="string"?s==null?void 0:s.document.querySelector(y):di(y);if(!N)return;let T;if(f&&(T=s.document.createElement("style"),T.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),s.document.head.appendChild(T)),M==="class"){const F=O.split(/\s/g);Object.values(h).flatMap(w=>(w||"").split(/\s/g)).filter(Boolean).forEach(w=>{F.includes(w)?N.classList.add(w):N.classList.remove(w)})}else N.setAttribute(M,O);f&&(s.getComputedStyle(T).opacity,document.head.removeChild(T))});function q(y){var M;$(t,n,(M=h[y])!=null?M:y)}function D(y){e.onChanged?e.onChanged(y,q):q(y)}Ne(I,D,{flush:"post",immediate:!0}),Wr(()=>D(I.value));const p=ne({get(){return a?C.value:I.value},set(y){C.value=y}});try{return Object.assign(p,{store:C,system:v,state:I})}catch{return p}}function xa(e={}){const{valueDark:t="dark",valueLight:n="",window:r=Oe}=e,s=Ca({...e,onChanged:(l,c)=>{var a;e.onChanged?(a=e.onChanged)==null||a.call(e,l==="dark",c,l):c(l)},modes:{dark:t,light:n}}),o=ne(()=>s.system?s.system.value:gi({window:r}).value?"dark":"light");return ne({get(){return s.value==="dark"},set(l){const c=l?"dark":"light";o.value===c?s.value="auto":s.value=c}})}function Qn(e){return typeof Window<"u"&&e instanceof Window?e.document.documentElement:typeof Document<"u"&&e instanceof Document?e.documentElement:e}function Su(e,t,n={}){const{window:r=Oe}=n;return qr(e,t,r==null?void 0:r.localStorage,n)}function mi(e){const t=window.getComputedStyle(e);if(t.overflowX==="scroll"||t.overflowY==="scroll"||t.overflowX==="auto"&&e.clientWidth<e.scrollWidth||t.overflowY==="auto"&&e.clientHeight<e.scrollHeight)return!0;{const n=e.parentNode;return!n||n.tagName==="BODY"?!1:mi(n)}}function Sa(e){const t=e||window.event,n=t.target;return mi(n)?!1:t.touches.length>1?!0:(t.preventDefault&&t.preventDefault(),!1)}const Zn=new WeakMap;function Tu(e,t=!1){const n=re(t);let r=null,s="";Ne(ui(e),l=>{const c=Qn(Fe(l));if(c){const a=c;if(Zn.get(a)||Zn.set(a,a.style.overflow),a.style.overflow!=="hidden"&&(s=a.style.overflow),a.style.overflow==="hidden")return n.value=!0;if(n.value)return a.style.overflow="hidden"}},{immediate:!0});const o=()=>{const l=Qn(Fe(e));!l||n.value||(Us&&(r=Ct(l,"touchmove",c=>{Sa(c)},{passive:!1})),l.style.overflow="hidden",n.value=!0)},i=()=>{const l=Qn(Fe(e));!l||!n.value||(Us&&(r==null||r()),l.style.overflow=s,Zn.delete(l),n.value=!1)};return Kr(i),ne({get(){return n.value},set(l){l?o():i()}})}function Au(e,t,n={}){const{window:r=Oe}=n;return qr(e,t,r==null?void 0:r.sessionStorage,n)}function Ru(e={}){const{window:t=Oe,behavior:n="auto"}=e;if(!t)return{x:re(0),y:re(0)};const r=re(t.scrollX),s=re(t.scrollY),o=ne({get(){return r.value},set(l){scrollTo({left:l,behavior:n})}}),i=ne({get(){return s.value},set(l){scrollTo({top:l,behavior:n})}});return Ct(t,"scroll",()=>{r.value=t.scrollX,s.value=t.scrollY},{capture:!1,passive:!0}),{x:o,y:i}}function Ou(e={}){const{window:t=Oe,initialWidth:n=Number.POSITIVE_INFINITY,initialHeight:r=Number.POSITIVE_INFINITY,listenOrientation:s=!0,includeScrollbar:o=!0}=e,i=re(n),l=re(r),c=()=>{t&&(o?(i.value=t.innerWidth,l.value=t.innerHeight):(i.value=t.document.documentElement.clientWidth,l.value=t.document.documentElement.clientHeight))};if(c(),Wr(c),Ct("resize",c,{passive:!0}),s){const a=hi("(orientation: portrait)");Ne(a,()=>c())}return{width:i,height:l}}var er={BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1},tr={};const yi=/^(?:[a-z]+:|\/\/)/i,Ta="vitepress-theme-appearance",Aa=/#.*$/,Ra=/[?#].*$/,Oa=/(?:(^|\/)index)?\.(?:md|html)$/,he=typeof document<"u",_i={relativePath:"404.md",filePath:"",title:"404",description:"Not Found",headers:[],frontmatter:{sidebar:!1,layout:"page"},lastUpdated:0,isNotFound:!0};function La(e,t,n=!1){if(t===void 0)return!1;if(e=ks(`/${e}`),n)return new RegExp(t).test(e);if(ks(t)!==e)return!1;const r=t.match(Aa);return r?(he?location.hash:"")===r[0]:!0}function ks(e){return decodeURI(e).replace(Ra,"").replace(Oa,"$1")}function Ia(e){return yi.test(e)}function Ma(e,t){return Object.keys((e==null?void 0:e.locales)||{}).find(n=>n!=="root"&&!Ia(n)&&La(t,`/${n}/`,!0))||"root"}function Pa(e,t){var r,s,o,i,l,c,a;const n=Ma(e,t);return Object.assign({},e,{localeIndex:n,lang:((r=e.locales[n])==null?void 0:r.lang)??e.lang,dir:((s=e.locales[n])==null?void 0:s.dir)??e.dir,title:((o=e.locales[n])==null?void 0:o.title)??e.title,titleTemplate:((i=e.locales[n])==null?void 0:i.titleTemplate)??e.titleTemplate,description:((l=e.locales[n])==null?void 0:l.description)??e.description,head:bi(e.head,((c=e.locales[n])==null?void 0:c.head)??[]),themeConfig:{...e.themeConfig,...(a=e.locales[n])==null?void 0:a.themeConfig}})}function vi(e,t){const n=t.title||e.title,r=t.titleTemplate??e.titleTemplate;if(typeof r=="string"&&r.includes(":title"))return r.replace(/:title/g,n);const s=Na(e.title,r);return n===s.slice(3)?n:`${n}${s}`}function Na(e,t){return t===!1?"":t===!0||t===void 0?` | ${e}`:e===t?"":` | ${t}`}function Fa(e,t){const[n,r]=t;if(n!=="meta")return!1;const s=Object.entries(r)[0];return s==null?!1:e.some(([o,i])=>o===n&&i[s[0]]===s[1])}function bi(e,t){return[...e.filter(n=>!Fa(t,n)),...t]}const $a=/[\u0000-\u001F"#$&*+,:;<=>?[\]^`{|}\u007F]/g,Ha=/^[a-z]:/i;function Ks(e){const t=Ha.exec(e),n=t?t[0]:"";return n+e.slice(n.length).replace($a,"_").replace(/(^|\/)_+(?=[^/]*$)/,"$1")}const nr=new Set;function ja(e){if(nr.size===0){const n=typeof process=="object"&&(tr==null?void 0:tr.VITE_EXTRA_EXTENSIONS)||(er==null?void 0:er.VITE_EXTRA_EXTENSIONS)||"";("3g2,3gp,aac,ai,apng,au,avif,bin,bmp,cer,class,conf,crl,css,csv,dll,doc,eps,epub,exe,gif,gz,ics,ief,jar,jpe,jpeg,jpg,js,json,jsonld,m4a,man,mid,midi,mjs,mov,mp2,mp3,mp4,mpe,mpeg,mpg,mpp,oga,ogg,ogv,ogx,opus,otf,p10,p7c,p7m,p7s,pdf,png,ps,qt,roff,rtf,rtx,ser,svg,t,tif,tiff,tr,ts,tsv,ttf,txt,vtt,wav,weba,webm,webp,woff,woff2,xhtml,xml,yaml,yml,zip"+(n&&typeof n=="string"?","+n:"")).split(",").forEach(r=>nr.add(r))}const t=e.split(".").pop();return t==null||!nr.has(t.toLowerCase())}function Lu(e){return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}const Va=Symbol(),ut=Fr(la);function Iu(e){const t=ne(()=>Pa(ut.value,e.data.relativePath)),n=t.value.appearance,r=n==="force-dark"?re(!0):n?xa({storageKey:Ta,initialValue:()=>typeof n=="string"?n:"auto",...typeof n=="object"?n:{}}):re(!1),s=re(he?location.hash:"");return he&&window.addEventListener("hashchange",()=>{s.value=location.hash}),Ne(()=>e.data,()=>{s.value=he?location.hash:""}),{site:t,theme:ne(()=>t.value.themeConfig),page:ne(()=>e.data),frontmatter:ne(()=>e.data.frontmatter),params:ne(()=>e.data.params),lang:ne(()=>t.value.lang),dir:ne(()=>e.data.frontmatter.dir||t.value.dir),localeIndex:ne(()=>t.value.localeIndex||"root"),title:ne(()=>vi(t.value,e.data)),description:ne(()=>e.data.description||t.value.description),isDark:r,hash:ne(()=>s.value)}}function Da(){const e=wt(Va);if(!e)throw new Error("vitepress data not properly injected in app");return e}function Ua(e,t){return`${e}${t}`.replace(/\/+/g,"/")}function Ws(e){return yi.test(e)||!e.startsWith("/")?e:Ua(ut.value.base,e)}function Ba(e){let t=e.replace(/\.html$/,"");if(t=decodeURIComponent(t),t=t.replace(/\/$/,"/index"),he){const n="/";t=Ks(t.slice(n.length).replace(/\//g,"_")||"index")+".md";let r=__VP_HASH_MAP__[t.toLowerCase()];if(r||(t=t.endsWith("_index.md")?t.slice(0,-9)+".md":t.slice(0,-3)+"_index.md",r=__VP_HASH_MAP__[t.toLowerCase()]),!r)return null;t=`${n}assets/${t}.${r}.js`}else t=`./${Ks(t.slice(1).replace(/\//g,"_"))}.md.js`;return t}let gn=[];function Mu(e){gn.push(e),$n(()=>{gn=gn.filter(t=>t!==e)})}function ka(){let e=ut.value.scrollOffset,t=0,n=24;if(typeof e=="object"&&"padding"in e&&(n=e.padding,e=e.selector),typeof e=="number")t=e;else if(typeof e=="string")t=qs(e,n);else if(Array.isArray(e))for(const r of e){const s=qs(r,n);if(s){t=s;break}}return t}function qs(e,t){const n=document.querySelector(e);if(!n)return 0;const r=n.getBoundingClientRect().bottom;return r<0?0:r+t}const Ka=Symbol(),wi="http://a.com",Wa=()=>({path:"/",component:null,data:_i});function Pu(e,t){const n=Rn(Wa()),r={route:n,go:s};async function s(l=he?location.href:"/"){var c,a;l=rr(l),await((c=r.onBeforeRouteChange)==null?void 0:c.call(r,l))!==!1&&(he&&l!==rr(location.href)&&(history.replaceState({scrollPosition:window.scrollY},""),history.pushState({},"",l)),await i(l),await((a=r.onAfterRouteChanged)==null?void 0:a.call(r,l)))}let o=null;async function i(l,c=0,a=!1){var m;if(await((m=r.onBeforePageLoad)==null?void 0:m.call(r,l))===!1)return;const f=new URL(l,wi),h=o=f.pathname;try{let v=await e(h);if(!v)throw new Error(`Page not found: ${h}`);if(o===h){o=null;const{default:C,__pageData:I}=v;if(!C)throw new Error(`Invalid route component: ${C}`);n.path=he?h:Ws(h),n.component=dn(C),n.data=dn(I),he&&Ln(()=>{let $=ut.value.base+I.relativePath.replace(/(?:(^|\/)index)?\.md$/,"$1");if(!ut.value.cleanUrls&&!$.endsWith("/")&&($+=".html"),$!==f.pathname&&(f.pathname=$,l=$+f.search+f.hash,history.replaceState({},"",l)),f.hash&&!c){let q=null;try{q=document.getElementById(decodeURIComponent(f.hash).slice(1))}catch(D){console.warn(D)}if(q){Gs(q,f.hash);return}}window.scrollTo(0,c)})}}catch(v){if(!/fetch|Page not found/.test(v.message)&&!/^\/404(\.html|\/)?$/.test(l)&&console.error(v),!a)try{const C=await fetch(ut.value.base+"hashmap.json");window.__VP_HASH_MAP__=await C.json(),await i(l,c,!0);return}catch{}if(o===h){o=null,n.path=he?h:Ws(h),n.component=t?dn(t):null;const C=he?h.replace(/(^|\/)$/,"$1index").replace(/(\.html)?$/,".md").replace(/^\//,""):"404.md";n.data={..._i,relativePath:C}}}}return he&&(history.state===null&&history.replaceState({},""),window.addEventListener("click",l=>{if(l.target.closest("button"))return;const a=l.target.closest("a");if(a&&!a.closest(".vp-raw")&&(a instanceof SVGElement||!a.download)){const{target:f}=a,{href:h,origin:m,pathname:v,hash:C,search:I}=new URL(a.href instanceof SVGAnimatedString?a.href.animVal:a.href,a.baseURI),$=new URL(location.href);!l.ctrlKey&&!l.shiftKey&&!l.altKey&&!l.metaKey&&!f&&m===$.origin&&ja(v)&&(l.preventDefault(),v===$.pathname&&I===$.search?(C!==$.hash&&(history.pushState({},"",h),window.dispatchEvent(new HashChangeEvent("hashchange",{oldURL:$.href,newURL:h}))),C?Gs(a,C,a.classList.contains("header-anchor")):window.scrollTo(0,0)):s(h))}},{capture:!0}),window.addEventListener("popstate",async l=>{var c;l.state!==null&&(await i(rr(location.href),l.state&&l.state.scrollPosition||0),(c=r.onAfterRouteChanged)==null||c.call(r,location.href))}),window.addEventListener("hashchange",l=>{l.preventDefault()})),r}function qa(){const e=wt(Ka);if(!e)throw new Error("useRouter() is called without provider.");return e}function Ei(){return qa().route}function Gs(e,t,n=!1){let r=null;try{r=e.classList.contains("header-anchor")?e:document.getElementById(decodeURIComponent(t).slice(1))}catch(s){console.warn(s)}if(r){let s=function(){!n||Math.abs(i-window.scrollY)>window.innerHeight?window.scrollTo(0,i):window.scrollTo({left:0,top:i,behavior:"smooth"})};const o=parseInt(window.getComputedStyle(r).paddingTop,10),i=window.scrollY+r.getBoundingClientRect().top-ka()+o;requestAnimationFrame(s)}}function rr(e){const t=new URL(e,wi);return t.pathname=t.pathname.replace(/(^|\/)index(\.html)?$/,"$1"),ut.value.cleanUrls?t.pathname=t.pathname.replace(/\.html$/,""):!t.pathname.endsWith("/")&&!t.pathname.endsWith(".html")&&(t.pathname+=".html"),t.pathname+t.search+t.hash}const sr=()=>gn.forEach(e=>e()),Nu=Vr({name:"VitePressContent",props:{as:{type:[Object,String],default:"div"}},setup(e){const t=Ei(),{site:n}=Da();return()=>br(e.as,n.value.contentProps??{style:{position:"relative"}},[t.component?br(t.component,{onVnodeMounted:sr,onVnodeUpdated:sr,onVnodeUnmounted:sr}):"404 Page Not Found"])}}),Fu=Vr({setup(e,{slots:t}){const n=re(!1);return xt(()=>{n.value=!0}),()=>n.value&&t.default?t.default():null}});function $u(){he&&window.addEventListener("click",e=>{var n;const t=e.target;if(t.matches(".vp-code-group input")){const r=(n=t.parentElement)==null?void 0:n.parentElement;if(!r)return;const s=Array.from(r.querySelectorAll("input")).indexOf(t);if(s<0)return;const o=r.querySelector(".blocks");if(!o)return;const i=Array.from(o.children).find(a=>a.classList.contains("active"));if(!i)return;const l=o.children[s];if(!l||i===l)return;i.classList.remove("active"),l.classList.add("active");const c=r==null?void 0:r.querySelector(`label[for="${t.id}"]`);c==null||c.scrollIntoView({block:"nearest"})}})}function Hu(){if(he){const e=new WeakMap;window.addEventListener("click",t=>{var r;const n=t.target;if(n.matches('div[class*="language-"] > button.copy')){const s=n.parentElement,o=(r=n.nextElementSibling)==null?void 0:r.nextElementSibling;if(!s||!o)return;const i=/language-(shellscript|shell|bash|sh|zsh)/.test(s.className),l=[".vp-copy-ignore",".diff.remove"],c=o.cloneNode(!0);c.querySelectorAll(l.join(",")).forEach(f=>f.remove());let a=c.textContent||"";i&&(a=a.replace(/^ *(\$|>) /gm,"").trim()),Ga(a).then(()=>{n.classList.add("copied"),clearTimeout(e.get(n));const f=setTimeout(()=>{n.classList.remove("copied"),n.blur(),e.delete(n)},2e3);e.set(n,f)})}})}}async function Ga(e){try{return navigator.clipboard.writeText(e)}catch{const t=document.createElement("textarea"),n=document.activeElement;t.value=e,t.setAttribute("readonly",""),t.style.contain="strict",t.style.position="absolute",t.style.left="-9999px",t.style.fontSize="12pt";const r=document.getSelection(),s=r?r.rangeCount>0&&r.getRangeAt(0):null;document.body.appendChild(t),t.select(),t.selectionStart=0,t.selectionEnd=e.length,document.execCommand("copy"),document.body.removeChild(t),s&&(r.removeAllRanges(),r.addRange(s)),n&&n.focus()}}function ju(e,t){let n=!0,r=[];const s=o=>{if(n){n=!1,o.forEach(l=>{const c=or(l);for(const a of document.head.children)if(a.isEqualNode(c)){r.push(a);return}});return}const i=o.map(or);r.forEach((l,c)=>{const a=i.findIndex(f=>f==null?void 0:f.isEqualNode(l??null));a!==-1?delete i[a]:(l==null||l.remove(),delete r[c])}),i.forEach(l=>l&&document.head.appendChild(l)),r=[...r,...i].filter(Boolean)};jr(()=>{const o=e.data,i=t.value,l=o&&o.description,c=o&&o.frontmatter.head||[],a=vi(i,o);a!==document.title&&(document.title=a);const f=l||i.description;let h=document.querySelector("meta[name=description]");h?h.getAttribute("content")!==f&&h.setAttribute("content",f):or(["meta",{name:"description",content:f}]),s(bi(i.head,Xa(c)))})}function or([e,t,n]){const r=document.createElement(e);for(const s in t)r.setAttribute(s,t[s]);return n&&(r.innerHTML=n),e==="script"&&!t.async&&(r.async=!1),r}function za(e){return e[0]==="meta"&&e[1]&&e[1].name==="description"}function Xa(e){return e.filter(t=>!za(t))}const ir=new Set,Ci=()=>document.createElement("link"),Ya=e=>{const t=Ci();t.rel="prefetch",t.href=e,document.head.appendChild(t)},Ja=e=>{const t=new XMLHttpRequest;t.open("GET",e,t.withCredentials=!0),t.send()};let an;const Qa=he&&(an=Ci())&&an.relList&&an.relList.supports&&an.relList.supports("prefetch")?Ya:Ja;function Vu(){if(!he||!window.IntersectionObserver)return;let e;if((e=navigator.connection)&&(e.saveData||/2g/.test(e.effectiveType)))return;const t=window.requestIdleCallback||setTimeout;let n=null;const r=()=>{n&&n.disconnect(),n=new IntersectionObserver(o=>{o.forEach(i=>{if(i.isIntersecting){const l=i.target;n.unobserve(l);const{pathname:c}=l;if(!ir.has(c)){ir.add(c);const a=Ba(c);a&&Qa(a)}}})}),t(()=>{document.querySelectorAll("#app a").forEach(o=>{const{hostname:i,pathname:l}=new URL(o.href instanceof SVGAnimatedString?o.href.animVal:o.href,o.baseURI),c=l.match(/\.\w+$/);c&&c[0]!==".html"||o.target!=="_blank"&&i===location.hostname&&(l!==location.pathname?n.observe(o):ir.add(l))})})};xt(r);const s=Ei();Ne(()=>s.path,r),$n(()=>{n&&n.disconnect()})}export{yu as $,su as A,Dl as B,ka as C,nu as D,lu as E,ye as F,Fr as G,Mu as H,oe as I,ru as J,yi as K,Ei as L,_c as M,wt as N,Ou as O,Sr as P,xu as Q,Ln as R,Ru as S,ri as T,he as U,On as V,iu as W,wu as X,Tu as Y,ec as Z,bu as _,Zo as a,au as a0,mu as a1,uu as a2,hu as a3,ju as a4,Ka as a5,Iu as a6,Va as a7,Nu as a8,Fu as a9,ut as aa,vu as ab,Pu as ac,Ba as ad,Vu as ae,Hu as af,$u as ag,br as ah,di as ai,Kr as aj,Cu as ak,Au as al,Su as am,Eu as an,qa as ao,Ct as ap,Mo as aq,ou as ar,gu as as,de as at,fu as au,dn as av,_u as aw,Lu as ax,Yo as b,du as c,Vr as d,pu as e,ja as f,Ws as g,ne as h,Ia as i,Qo as j,_o as k,tu as l,La as m,Tr as n,zo as o,eu as p,hi as q,cu as r,re as s,Za as t,Da as u,Ne as v,Cl as w,jr as x,xt as y,$n as z};
