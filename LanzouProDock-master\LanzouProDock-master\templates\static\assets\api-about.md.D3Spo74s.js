import{_ as t,c as e,o as l,a3 as a}from"./chunks/framework.D42TvibZ.js";const m=JSON.parse('{"title":"关于我 🧑‍💻","description":"","frontmatter":{"title":"关于我 🧑‍💻","layout":"home","hero":{"name":"Evan🕊️","text":"探索技术，追求卓越","tagline":"I love gale and spirits, loneliness and freedom.","actions":[{"theme":"brand","text":"了解更多 →","link":"#关于我"},{"theme":"alt","text":"项目概览 →","link":"#项目"}]}},"headers":[],"relativePath":"api-about.md","filePath":"api-about.md","lastUpdated":1740580269000}'),i={name:"api-about.md"},o=a('<h1 id="关于我" tabindex="-1">关于我 <a class="header-anchor" href="#关于我" aria-label="Permalink to &quot;关于我&quot;">​</a></h1><div class="tip custom-block"><p class="custom-block-title">初衷</p><p>我是谁不重要，重要的是工具能否为您带来价值。这里是关于我的一些信息，以及我对技术的热爱与追求。</p></div><h2 id="心境历程" tabindex="-1">心境历程 <a class="header-anchor" href="#心境历程" aria-label="Permalink to &quot;心境历程&quot;">​</a></h2><p>当我打开编辑器，代码在黑暗中闪烁着微光。 我感到由衷的喜悦，因为每一行代码都承载着无限可能。 在技术的海洋里，我选择做一个执着的探索者，用创新解决每一个难题。 我珍惜与开源社区同行们的交流，用技术为这个世界添砖加瓦。 或许道路不会一帆风顺，但我愿意以积极的心态面对每一个挑战， 因为我相信，技术的价值在于让生活变得更美好。</p><h2 id="时间轴" tabindex="-1">时间轴 <a class="header-anchor" href="#时间轴" aria-label="Permalink to &quot;时间轴&quot;">​</a></h2><div class="timeline-dot"><span class="timeline-dot-title">2024 ~ *</span><p>憧憬未来的无限可能</p></div><div class="timeline-dot"><span class="timeline-dot-title">2024 - 07 - 16</span><p>步入人生的第三个十年</p></div><div class="timeline-dot"><span class="timeline-dot-title">2004 ~ 2024</span><p>编织技术梦想的漫长岁月</p></div><div class="timeline-dot"><span class="timeline-dot-title">2004 - 07 - 16</span><p>开启人生旅程</p></div><div class="timeline-dot"><span class="timeline-dot-title">* ~ 2004</span><p>未知的过往</p></div><h2 id="项目" tabindex="-1">项目 <a class="header-anchor" href="#项目" aria-label="Permalink to &quot;项目&quot;">​</a></h2><p>以下是我近期参与开发的一些项目：</p><div class="info custom-block"><p class="custom-block-title">### UY云后台管理系统</p><ul><li><strong>描述：</strong> ☁️ 专注解决个人开发者需求的轻量级后台管理系统</li><li><strong>特点：</strong> 简洁高效、功能完备、易于扩展</li><li><strong>链接：</strong> <a href="https://uyclouds.com" target="_blank" rel="noreferrer">UY云后台管理系统</a></li></ul></div><div class="info custom-block"><p class="custom-block-title">### Wow-Note云储笔记</p><ul><li><strong>描述：</strong> 📔 基于微服务架构的现代化云笔记系统</li><li><strong>特点：</strong> 高可用、数据安全、便捷同步</li><li><strong>链接：</strong> <a href="https://gitee.com/uyevan/wow-note-spring" target="_blank" rel="noreferrer">Wow-Note云储笔记</a></li></ul></div><div class="info custom-block"><p class="custom-block-title">### HamBuk影视</p><ul><li><strong>描述：</strong> 🎞️ 基于Flutter开发的多语言影视平台</li><li><strong>特点：</strong> 跨平台、性能优异、用户体验佳</li><li><strong>链接：</strong> <a href="https://gitee.com/uyevan/hambuk" target="_blank" rel="noreferrer">HamBuk影视</a></li></ul></div><div class="info custom-block"><p class="custom-block-title">### UyClub实用工具箱</p><ul><li><strong>描述：</strong> 🛠️ 全能型效率工具集合</li><li><strong>特点：</strong> 功能丰富、操作便捷、持续更新</li><li><strong>链接：</strong> <a href="https://gitee.com/uyevan/uy-tool-club" target="_blank" rel="noreferrer">UyClub实用工具箱</a></li></ul></div><div class="info custom-block"><p class="custom-block-title">### LeYing软件库</p><ul><li><strong>描述：</strong> 📱 Android平台应用分发平台</li><li><strong>特点：</strong> 稳定可靠、持续维护</li><li><strong>链接：</strong> <a href="https://gitee.com/uyevan/leying" target="_blank" rel="noreferrer">LeYing软件库</a></li></ul></div><h2 id="问题反馈" tabindex="-1">问题反馈 <a class="header-anchor" href="#问题反馈" aria-label="Permalink to &quot;问题反馈&quot;">​</a></h2><div class="warning custom-block"><p class="custom-block-title">问题报告</p><p>如果您在使用过程中遇到任何问题或有改进建议，欢迎通过以下方式提交：</p><ol><li>在 GitHub 项目页面提交 Issue</li><li>发送详细问题描述至我的邮箱</li><li>提供问题复现步骤和相关日志</li></ol><p>您的反馈是我们不断进步的动力！</p></div><h2 id="免责声明" tabindex="-1">免责声明 <a class="header-anchor" href="#免责声明" aria-label="Permalink to &quot;免责声明&quot;">​</a></h2><div class="danger custom-block"><p class="custom-block-title">免责声明</p><ol><li>本项目开源软件仅供学习交流使用，严禁用于任何商业用途或违法行为</li><li>使用本项目软件造成的任何直接或间接损失，开发者不承担任何责任</li><li>请务必遵守相关法律法规，合法合规使用本项目提供的功能</li><li>若发现本项目有任何侵权行为，请立即联系我们进行处理</li></ol></div><h2 id="联系方式" tabindex="-1">联系方式 <a class="header-anchor" href="#联系方式" aria-label="Permalink to &quot;联系方式&quot;">​</a></h2><p>如果您有任何问题或建议，欢迎通过以下方式联系：</p><ul><li><strong>📮 邮箱：</strong> <a href="mailto:<EMAIL>" target="_blank" rel="noreferrer"><EMAIL></a></li><li><strong>🌐 主页：</strong> <a href="https://jfkj.xyz" target="_blank" rel="noreferrer">Personal Page</a></li><li><strong>💻 GitHub：</strong> <a href="https://github.com/uyevan" target="_blank" rel="noreferrer">GitHub</a></li><li><strong>🌿 Gitee：</strong> <a href="https://gitee.com/uyevan" target="_blank" rel="noreferrer">Gitee</a></li></ul><div class="tip custom-block"><p class="custom-block-title">备注</p><p>联系我时请注明来意，以便更好地为您服务！</p></div>',25),s=[o];function r(n,c,d,p,u,h){return l(),e("div",null,s)}const b=t(i,[["render",r]]);export{m as __pageData,b as default};
