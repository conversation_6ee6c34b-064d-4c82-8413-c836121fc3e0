const __vite__fileDeps=["assets/chunks/VPLocalSearchBox.DIR2B3hV.js","assets/chunks/framework.D42TvibZ.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{d as _,o as a,c,r as l,n as N,a as D,t as I,b as $,w as d,e as f,T as ve,_ as b,u as Ue,i as Ge,f as je,g as pe,h as y,j as v,k as r,p as B,l as H,m as z,q as ie,s as w,v as j,x as Z,y as R,z as he,A as ye,B as ze,C as qe,D as q,F as M,E,G as Pe,H as x,I as m,J as W,K as Le,L as ee,M as Y,N as te,O as Ke,P as Ve,Q as le,R as We,S as Se,U as oe,V as Re,W as Je,X as Ye,Y as Te,Z as Ie,$ as Qe,a0 as Xe,a1 as Ze,a2 as xe}from"./framework.D42TvibZ.js";const et=_({__name:"VPBadge",props:{text:{},type:{default:"tip"}},setup(o){return(e,t)=>(a(),c("span",{class:N(["VPBadge",e.type])},[l(e.$slots,"default",{},()=>[D(I(e.text),1)])],2))}}),tt={key:0,class:"VPBackdrop"},ot=_({__name:"VPBackdrop",props:{show:{type:Boolean}},setup(o){return(e,t)=>(a(),$(ve,{name:"fade"},{default:d(()=>[e.show?(a(),c("div",tt)):f("",!0)]),_:1}))}}),st=b(ot,[["__scopeId","data-v-54a304ca"]]),P=Ue;function nt(o,e){let t,n=!1;return()=>{t&&clearTimeout(t),n?t=setTimeout(o,e):(o(),(n=!0)&&setTimeout(()=>n=!1,e))}}function ce(o){return/^\//.test(o)?o:`/${o}`}function fe(o){const{pathname:e,search:t,hash:n,protocol:s}=new URL(o,"http://a.com");if(Ge(o)||o.startsWith("#")||!s.startsWith("http")||!je(e))return o;const{site:i}=P(),u=e.endsWith("/")||e.endsWith(".html")?o:o.replace(/(?:(^\.+)\/)?.*$/,`$1${e.replace(/(\.md)?$/,i.value.cleanUrls?"":".html")}${t}${n}`);return pe(u)}function J({correspondingLink:o=!1}={}){const{site:e,localeIndex:t,page:n,theme:s,hash:i}=P(),u=y(()=>{var p,g;return{label:(p=e.value.locales[t.value])==null?void 0:p.label,link:((g=e.value.locales[t.value])==null?void 0:g.link)||(t.value==="root"?"/":`/${t.value}/`)}});return{localeLinks:y(()=>Object.entries(e.value.locales).flatMap(([p,g])=>u.value.label===g.label?[]:{text:g.label,link:at(g.link||(p==="root"?"/":`/${p}/`),s.value.i18nRouting!==!1&&o,n.value.relativePath.slice(u.value.link.length-1),!e.value.cleanUrls)+i.value})),currentLang:u}}function at(o,e,t,n){return e?o.replace(/\/$/,"")+ce(t.replace(/(^|\/)index\.md$/,"$1").replace(/\.md$/,n?".html":"")):o}const rt=o=>(B("data-v-6ff51ddd"),o=o(),H(),o),it={class:"NotFound"},lt={class:"code"},ct={class:"title"},ut=rt(()=>v("div",{class:"divider"},null,-1)),dt={class:"quote"},vt={class:"action"},pt=["href","aria-label"],ht=_({__name:"NotFound",setup(o){const{theme:e}=P(),{currentLang:t}=J();return(n,s)=>{var i,u,h,p,g;return a(),c("div",it,[v("p",lt,I(((i=r(e).notFound)==null?void 0:i.code)??"404"),1),v("h1",ct,I(((u=r(e).notFound)==null?void 0:u.title)??"PAGE NOT FOUND"),1),ut,v("blockquote",dt,I(((h=r(e).notFound)==null?void 0:h.quote)??"But if you don't change your direction, and if you keep looking, you may end up where you are heading."),1),v("div",vt,[v("a",{class:"link",href:r(pe)(r(t).link),"aria-label":((p=r(e).notFound)==null?void 0:p.linkLabel)??"go to home"},I(((g=r(e).notFound)==null?void 0:g.linkText)??"Take me home"),9,pt)])])}}}),ft=b(ht,[["__scopeId","data-v-6ff51ddd"]]);function we(o,e){if(Array.isArray(o))return Q(o);if(o==null)return[];e=ce(e);const t=Object.keys(o).sort((s,i)=>i.split("/").length-s.split("/").length).find(s=>e.startsWith(ce(s))),n=t?o[t]:[];return Array.isArray(n)?Q(n):Q(n.items,n.base)}function _t(o){const e=[];let t=0;for(const n in o){const s=o[n];if(s.items){t=e.push(s);continue}e[t]||e.push({items:[]}),e[t].items.push(s)}return e}function mt(o){const e=[];function t(n){for(const s of n)s.text&&s.link&&e.push({text:s.text,link:s.link,docFooterText:s.docFooterText}),s.items&&t(s.items)}return t(o),e}function ue(o,e){return Array.isArray(e)?e.some(t=>ue(o,t)):z(o,e.link)?!0:e.items?ue(o,e.items):!1}function Q(o,e){return[...o].map(t=>{const n={...t},s=n.base||e;return s&&n.link&&(n.link=s+n.link),n.items&&(n.items=Q(n.items,s)),n})}function O(){const{frontmatter:o,page:e,theme:t}=P(),n=ie("(min-width: 960px)"),s=w(!1),i=y(()=>{const C=t.value.sidebar,S=e.value.relativePath;return C?we(C,S):[]}),u=w(i.value);j(i,(C,S)=>{JSON.stringify(C)!==JSON.stringify(S)&&(u.value=i.value)});const h=y(()=>o.value.sidebar!==!1&&u.value.length>0&&o.value.layout!=="home"),p=y(()=>g?o.value.aside==null?t.value.aside==="left":o.value.aside==="left":!1),g=y(()=>o.value.layout==="home"?!1:o.value.aside!=null?!!o.value.aside:t.value.aside!==!1),L=y(()=>h.value&&n.value),k=y(()=>h.value?_t(u.value):[]);function V(){s.value=!0}function T(){s.value=!1}function A(){s.value?T():V()}return{isOpen:s,sidebar:u,sidebarGroups:k,hasSidebar:h,hasAside:g,leftAside:p,isSidebarEnabled:L,open:V,close:T,toggle:A}}function kt(o,e){let t;Z(()=>{t=o.value?document.activeElement:void 0}),R(()=>{window.addEventListener("keyup",n)}),he(()=>{window.removeEventListener("keyup",n)});function n(s){s.key==="Escape"&&o.value&&(e(),t==null||t.focus())}}function bt(o){const{page:e,hash:t}=P(),n=w(!1),s=y(()=>o.value.collapsed!=null),i=y(()=>!!o.value.link),u=w(!1),h=()=>{u.value=z(e.value.relativePath,o.value.link)};j([e,o,t],h),R(h);const p=y(()=>u.value?!0:o.value.items?ue(e.value.relativePath,o.value.items):!1),g=y(()=>!!(o.value.items&&o.value.items.length));Z(()=>{n.value=!!(s.value&&o.value.collapsed)}),ye(()=>{(u.value||p.value)&&(n.value=!1)});function L(){s.value&&(n.value=!n.value)}return{collapsed:n,collapsible:s,isLink:i,isActiveLink:u,hasActiveLink:p,hasChildren:g,toggle:L}}function $t(){const{hasSidebar:o}=O(),e=ie("(min-width: 960px)"),t=ie("(min-width: 1280px)");return{isAsideEnabled:y(()=>!t.value&&!e.value?!1:o.value?t.value:e.value)}}const de=[];function Ne(o){return typeof o.outline=="object"&&!Array.isArray(o.outline)&&o.outline.label||o.outlineTitle||"This page 🏷️"}function _e(o){const e=[...document.querySelectorAll(".VPDoc :where(h1,h2,h3,h4,h5,h6)")].filter(t=>t.id&&t.hasChildNodes()).map(t=>{const n=Number(t.tagName[1]);return{element:t,title:gt(t),link:"#"+t.id,level:n}});return yt(e,o)}function gt(o){let e="";for(const t of o.childNodes)if(t.nodeType===1){if(t.classList.contains("VPBadge")||t.classList.contains("header-anchor")||t.classList.contains("ignore-header"))continue;e+=t.textContent}else t.nodeType===3&&(e+=t.textContent);return e.trim()}function yt(o,e){if(e===!1)return[];const t=(typeof e=="object"&&!Array.isArray(e)?e.level:e)||2,[n,s]=typeof t=="number"?[t,t]:t==="deep"?[2,6]:t;o=o.filter(u=>u.level>=n&&u.level<=s),de.length=0;for(const{element:u,link:h}of o)de.push({element:u,link:h});const i=[];e:for(let u=0;u<o.length;u++){const h=o[u];if(u===0)i.push(h);else{for(let p=u-1;p>=0;p--){const g=o[p];if(g.level<h.level){(g.children||(g.children=[])).push(h);continue e}}i.push(h)}}return i}function Pt(o,e){const{isAsideEnabled:t}=$t(),n=nt(i,100);let s=null;R(()=>{requestAnimationFrame(i),window.addEventListener("scroll",n)}),ze(()=>{u(location.hash)}),he(()=>{window.removeEventListener("scroll",n)});function i(){if(!t.value)return;const h=window.scrollY,p=window.innerHeight,g=document.body.offsetHeight,L=Math.abs(h+p-g)<1,k=de.map(({element:T,link:A})=>({link:A,top:Lt(T)})).filter(({top:T})=>!Number.isNaN(T)).sort((T,A)=>T.top-A.top);if(!k.length){u(null);return}if(h<1){u(null);return}if(L){u(k[k.length-1].link);return}let V=null;for(const{link:T,top:A}of k){if(A>h+qe()+4)break;V=T}u(V)}function u(h){s&&s.classList.remove("active"),h==null?s=null:s=o.value.querySelector(`a[href="${decodeURIComponent(h)}"]`);const p=s;p?(p.classList.add("active"),e.value.style.top=p.offsetTop+39+"px",e.value.style.opacity="1"):(e.value.style.top="33px",e.value.style.opacity="0")}}function Lt(o){let e=0;for(;o!==document.body;){if(o===null)return NaN;e+=o.offsetTop,o=o.offsetParent}return e}const Vt=["href","title"],St=_({__name:"VPDocOutlineItem",props:{headers:{},root:{type:Boolean}},setup(o){function e({target:t}){const n=t.href.split("#")[1],s=document.getElementById(decodeURIComponent(n));s==null||s.focus({preventScroll:!0})}return(t,n)=>{const s=q("VPDocOutlineItem",!0);return a(),c("ul",{class:N(["VPDocOutlineItem",t.root?"root":"nested"])},[(a(!0),c(M,null,E(t.headers,({children:i,link:u,title:h})=>(a(),c("li",null,[v("a",{class:"outline-link",href:u,onClick:e,title:h},I(h),9,Vt),i!=null&&i.length?(a(),$(s,{key:0,headers:i},null,8,["headers"])):f("",!0)]))),256))],2)}}}),Me=b(St,[["__scopeId","data-v-53c99d69"]]),Tt={class:"content"},It={"aria-level":"2",class:"outline-title",id:"doc-outline-aria-label",role:"heading"},wt=_({__name:"VPDocAsideOutline",setup(o){const{frontmatter:e,theme:t}=P(),n=Pe([]);x(()=>{n.value=_e(e.value.outline??t.value.outline)});const s=w(),i=w();return Pt(s,i),(u,h)=>(a(),c("nav",{"aria-labelledby":"doc-outline-aria-label",class:N(["VPDocAsideOutline",{"has-outline":n.value.length>0}]),ref_key:"container",ref:s},[v("div",Tt,[v("div",{class:"outline-marker",ref_key:"marker",ref:i},null,512),v("div",It,I(r(Ne)(r(t))),1),m(Me,{headers:n.value,root:!0},null,8,["headers"])])],2))}}),Nt=b(wt,[["__scopeId","data-v-f610f197"]]),Mt={class:"VPDocAsideCarbonAds"},At=_({__name:"VPDocAsideCarbonAds",props:{carbonAds:{}},setup(o){const e=()=>null;return(t,n)=>(a(),c("div",Mt,[m(r(e),{"carbon-ads":t.carbonAds},null,8,["carbon-ads"])]))}}),Ct=o=>(B("data-v-cb998dce"),o=o(),H(),o),Bt={class:"VPDocAside"},Ht=Ct(()=>v("div",{class:"spacer"},null,-1)),Et=_({__name:"VPDocAside",setup(o){const{theme:e}=P();return(t,n)=>(a(),c("div",Bt,[l(t.$slots,"aside-top",{},void 0,!0),l(t.$slots,"aside-outline-before",{},void 0,!0),m(Nt),l(t.$slots,"aside-outline-after",{},void 0,!0),Ht,l(t.$slots,"aside-ads-before",{},void 0,!0),r(e).carbonAds?(a(),$(At,{key:0,"carbon-ads":r(e).carbonAds},null,8,["carbon-ads"])):f("",!0),l(t.$slots,"aside-ads-after",{},void 0,!0),l(t.$slots,"aside-bottom",{},void 0,!0)]))}}),Ft=b(Et,[["__scopeId","data-v-cb998dce"]]);function Dt(){const{theme:o,page:e}=P();return y(()=>{const{text:t="Edit this page",pattern:n=""}=o.value.editLink||{};let s;return typeof n=="function"?s=n(e.value):s=n.replace(/:path/g,e.value.filePath),{url:s,text:t}})}function Ot(){const{page:o,theme:e,frontmatter:t}=P();return y(()=>{var g,L,k,V,T,A,C,S;const n=we(e.value.sidebar,o.value.relativePath),s=mt(n),i=Ut(s,U=>U.link.replace(/[?#].*$/,"")),u=i.findIndex(U=>z(o.value.relativePath,U.link)),h=((g=e.value.docFooter)==null?void 0:g.prev)===!1&&!t.value.prev||t.value.prev===!1,p=((L=e.value.docFooter)==null?void 0:L.next)===!1&&!t.value.next||t.value.next===!1;return{prev:h?void 0:{text:(typeof t.value.prev=="string"?t.value.prev:typeof t.value.prev=="object"?t.value.prev.text:void 0)??((k=i[u-1])==null?void 0:k.docFooterText)??((V=i[u-1])==null?void 0:V.text),link:(typeof t.value.prev=="object"?t.value.prev.link:void 0)??((T=i[u-1])==null?void 0:T.link)},next:p?void 0:{text:(typeof t.value.next=="string"?t.value.next:typeof t.value.next=="object"?t.value.next.text:void 0)??((A=i[u+1])==null?void 0:A.docFooterText)??((C=i[u+1])==null?void 0:C.text),link:(typeof t.value.next=="object"?t.value.next.link:void 0)??((S=i[u+1])==null?void 0:S.link)}}})}function Ut(o,e){const t=new Set;return o.filter(n=>{const s=e(n);return t.has(s)?!1:t.add(s)})}const F=_({__name:"VPLink",props:{tag:{},href:{},noIcon:{type:Boolean},target:{},rel:{}},setup(o){const e=o,t=y(()=>e.tag??(e.href?"a":"span")),n=y(()=>e.href&&Le.test(e.href)||e.target==="_blank");return(s,i)=>(a(),$(W(t.value),{class:N(["VPLink",{link:s.href,"vp-external-link-icon":n.value,"no-icon":s.noIcon}]),href:s.href?r(fe)(s.href):void 0,target:s.target??(n.value?"_blank":void 0),rel:s.rel??(n.value?"noreferrer":void 0)},{default:d(()=>[l(s.$slots,"default")]),_:3},8,["class","href","target","rel"]))}}),Gt={class:"VPLastUpdated"},jt=["datetime"],zt=_({__name:"VPDocFooterLastUpdated",setup(o){const{theme:e,page:t,frontmatter:n,lang:s}=P(),i=y(()=>new Date(n.value.lastUpdated??t.value.lastUpdated)),u=y(()=>i.value.toISOString()),h=w("");return R(()=>{Z(()=>{var p,g,L;h.value=new Intl.DateTimeFormat((g=(p=e.value.lastUpdated)==null?void 0:p.formatOptions)!=null&&g.forceLocale?s.value:void 0,((L=e.value.lastUpdated)==null?void 0:L.formatOptions)??{dateStyle:"short",timeStyle:"short"}).format(i.value)})}),(p,g)=>{var L;return a(),c("p",Gt,[D(I(((L=r(e).lastUpdated)==null?void 0:L.text)||r(e).lastUpdatedText||"Last updated")+": ",1),v("time",{datetime:u.value},I(h.value),9,jt)])}}}),qt=b(zt,[["__scopeId","data-v-19a7ae4e"]]),Ae=o=>(B("data-v-5941af80"),o=o(),H(),o),Kt={key:0,class:"VPDocFooter"},Wt={key:0,class:"edit-info"},Rt={key:0,class:"edit-link"},Jt=Ae(()=>v("span",{class:"vpi-square-pen edit-link-icon"},null,-1)),Yt={key:1,class:"last-updated"},Qt={key:1,class:"prev-next","aria-labelledby":"doc-footer-aria-label"},Xt=Ae(()=>v("span",{class:"visually-hidden",id:"doc-footer-aria-label"},"Pager",-1)),Zt={class:"pager"},xt=["innerHTML"],eo=["innerHTML"],to={class:"pager"},oo=["innerHTML"],so=["innerHTML"],no=_({__name:"VPDocFooter",setup(o){const{theme:e,page:t,frontmatter:n}=P(),s=Dt(),i=Ot(),u=y(()=>e.value.editLink&&n.value.editLink!==!1),h=y(()=>t.value.lastUpdated&&n.value.lastUpdated!==!1),p=y(()=>u.value||h.value||i.value.prev||i.value.next);return(g,L)=>{var k,V,T,A;return p.value?(a(),c("footer",Kt,[l(g.$slots,"doc-footer-before",{},void 0,!0),u.value||h.value?(a(),c("div",Wt,[u.value?(a(),c("div",Rt,[m(F,{class:"edit-link-button",href:r(s).url,"no-icon":!0},{default:d(()=>[Jt,D(" "+I(r(s).text),1)]),_:1},8,["href"])])):f("",!0),h.value?(a(),c("div",Yt,[m(qt)])):f("",!0)])):f("",!0),(k=r(i).prev)!=null&&k.link||(V=r(i).next)!=null&&V.link?(a(),c("nav",Qt,[Xt,v("div",Zt,[(T=r(i).prev)!=null&&T.link?(a(),$(F,{key:0,class:"pager-link prev",href:r(i).prev.link},{default:d(()=>{var C;return[v("span",{class:"desc",innerHTML:((C=r(e).docFooter)==null?void 0:C.prev)||"Previous page"},null,8,xt),v("span",{class:"title",innerHTML:r(i).prev.text},null,8,eo)]}),_:1},8,["href"])):f("",!0)]),v("div",to,[(A=r(i).next)!=null&&A.link?(a(),$(F,{key:0,class:"pager-link next",href:r(i).next.link},{default:d(()=>{var C;return[v("span",{class:"desc",innerHTML:((C=r(e).docFooter)==null?void 0:C.next)||"Next page"},null,8,oo),v("span",{class:"title",innerHTML:r(i).next.text},null,8,so)]}),_:1},8,["href"])):f("",!0)])])):f("",!0)])):f("",!0)}}}),ao=b(no,[["__scopeId","data-v-5941af80"]]),ro=o=>(B("data-v-e6f2a212"),o=o(),H(),o),io={class:"container"},lo=ro(()=>v("div",{class:"aside-curtain"},null,-1)),co={class:"aside-container"},uo={class:"aside-content"},vo={class:"content"},po={class:"content-container"},ho={class:"main"},fo=_({__name:"VPDoc",setup(o){const{theme:e}=P(),t=ee(),{hasSidebar:n,hasAside:s,leftAside:i}=O(),u=y(()=>t.path.replace(/[./]+/g,"_").replace(/_html$/,""));return(h,p)=>{const g=q("Content");return a(),c("div",{class:N(["VPDoc",{"has-sidebar":r(n),"has-aside":r(s)}])},[l(h.$slots,"doc-top",{},void 0,!0),v("div",io,[r(s)?(a(),c("div",{key:0,class:N(["aside",{"left-aside":r(i)}])},[lo,v("div",co,[v("div",uo,[m(Ft,null,{"aside-top":d(()=>[l(h.$slots,"aside-top",{},void 0,!0)]),"aside-bottom":d(()=>[l(h.$slots,"aside-bottom",{},void 0,!0)]),"aside-outline-before":d(()=>[l(h.$slots,"aside-outline-before",{},void 0,!0)]),"aside-outline-after":d(()=>[l(h.$slots,"aside-outline-after",{},void 0,!0)]),"aside-ads-before":d(()=>[l(h.$slots,"aside-ads-before",{},void 0,!0)]),"aside-ads-after":d(()=>[l(h.$slots,"aside-ads-after",{},void 0,!0)]),_:3})])])],2)):f("",!0),v("div",vo,[v("div",po,[l(h.$slots,"doc-before",{},void 0,!0),v("main",ho,[m(g,{class:N(["vp-doc",[u.value,r(e).externalLinkIcon&&"external-link-icon-enabled"]])},null,8,["class"])]),m(ao,null,{"doc-footer-before":d(()=>[l(h.$slots,"doc-footer-before",{},void 0,!0)]),_:3}),l(h.$slots,"doc-after",{},void 0,!0)])])]),l(h.$slots,"doc-bottom",{},void 0,!0)],2)}}}),_o=b(fo,[["__scopeId","data-v-e6f2a212"]]),mo=_({__name:"VPButton",props:{tag:{},size:{default:"medium"},theme:{default:"brand"},text:{},href:{},target:{},rel:{}},setup(o){const e=o,t=y(()=>e.href&&Le.test(e.href)),n=y(()=>e.tag||e.href?"a":"button");return(s,i)=>(a(),$(W(n.value),{class:N(["VPButton",[s.size,s.theme]]),href:s.href?r(fe)(s.href):void 0,target:e.target??(t.value?"_blank":void 0),rel:e.rel??(t.value?"noreferrer":void 0)},{default:d(()=>[D(I(s.text),1)]),_:1},8,["class","href","target","rel"]))}}),ko=b(mo,[["__scopeId","data-v-c9cf0e3c"]]),bo=["src","alt"],$o=_({inheritAttrs:!1,__name:"VPImage",props:{image:{},alt:{}},setup(o){return(e,t)=>{const n=q("VPImage",!0);return e.image?(a(),c(M,{key:0},[typeof e.image=="string"||"src"in e.image?(a(),c("img",Y({key:0,class:"VPImage"},typeof e.image=="string"?e.$attrs:{...e.image,...e.$attrs},{src:r(pe)(typeof e.image=="string"?e.image:e.image.src),alt:e.alt??(typeof e.image=="string"?"":e.image.alt||"")}),null,16,bo)):(a(),c(M,{key:1},[m(n,Y({class:"dark",image:e.image.dark,alt:e.image.alt},e.$attrs),null,16,["image","alt"]),m(n,Y({class:"light",image:e.image.light,alt:e.image.alt},e.$attrs),null,16,["image","alt"])],64))],64)):f("",!0)}}}),X=b($o,[["__scopeId","data-v-ab19afbb"]]),go=o=>(B("data-v-b10c5094"),o=o(),H(),o),yo={class:"container"},Po={class:"main"},Lo={key:0,class:"name"},Vo=["innerHTML"],So=["innerHTML"],To=["innerHTML"],Io={key:0,class:"actions"},wo={key:0,class:"image"},No={class:"image-container"},Mo=go(()=>v("div",{class:"image-bg"},null,-1)),Ao=_({__name:"VPHero",props:{name:{},text:{},tagline:{},image:{},actions:{}},setup(o){const e=te("hero-image-slot-exists");return(t,n)=>(a(),c("div",{class:N(["VPHero",{"has-image":t.image||r(e)}])},[v("div",yo,[v("div",Po,[l(t.$slots,"home-hero-info-before",{},void 0,!0),l(t.$slots,"home-hero-info",{},()=>[t.name?(a(),c("h1",Lo,[v("span",{innerHTML:t.name,class:"clip"},null,8,Vo)])):f("",!0),t.text?(a(),c("p",{key:1,innerHTML:t.text,class:"text"},null,8,So)):f("",!0),t.tagline?(a(),c("p",{key:2,innerHTML:t.tagline,class:"tagline"},null,8,To)):f("",!0)],!0),l(t.$slots,"home-hero-info-after",{},void 0,!0),t.actions?(a(),c("div",Io,[(a(!0),c(M,null,E(t.actions,s=>(a(),c("div",{key:s.link,class:"action"},[m(ko,{tag:"a",size:"medium",theme:s.theme,text:s.text,href:s.link,target:s.target,rel:s.rel},null,8,["theme","text","href","target","rel"])]))),128))])):f("",!0),l(t.$slots,"home-hero-actions-after",{},void 0,!0)]),t.image||r(e)?(a(),c("div",wo,[v("div",No,[Mo,l(t.$slots,"home-hero-image",{},()=>[t.image?(a(),$(X,{key:0,class:"image-src",image:t.image},null,8,["image"])):f("",!0)],!0)])])):f("",!0)])],2))}}),Co=b(Ao,[["__scopeId","data-v-b10c5094"]]),Bo=_({__name:"VPHomeHero",setup(o){const{frontmatter:e}=P();return(t,n)=>r(e).hero?(a(),$(Co,{key:0,class:"VPHomeHero",name:r(e).hero.name,text:r(e).hero.text,tagline:r(e).hero.tagline,image:r(e).hero.image,actions:r(e).hero.actions},{"home-hero-info-before":d(()=>[l(t.$slots,"home-hero-info-before")]),"home-hero-info":d(()=>[l(t.$slots,"home-hero-info")]),"home-hero-info-after":d(()=>[l(t.$slots,"home-hero-info-after")]),"home-hero-actions-after":d(()=>[l(t.$slots,"home-hero-actions-after")]),"home-hero-image":d(()=>[l(t.$slots,"home-hero-image")]),_:3},8,["name","text","tagline","image","actions"])):f("",!0)}}),Ho=o=>(B("data-v-bd37d1a2"),o=o(),H(),o),Eo={class:"box"},Fo={key:0,class:"icon"},Do=["innerHTML"],Oo=["innerHTML"],Uo=["innerHTML"],Go={key:4,class:"link-text"},jo={class:"link-text-value"},zo=Ho(()=>v("span",{class:"vpi-arrow-right link-text-icon"},null,-1)),qo=_({__name:"VPFeature",props:{icon:{},title:{},details:{},link:{},linkText:{},rel:{},target:{}},setup(o){return(e,t)=>(a(),$(F,{class:"VPFeature",href:e.link,rel:e.rel,target:e.target,"no-icon":!0,tag:e.link?"a":"div"},{default:d(()=>[v("article",Eo,[typeof e.icon=="object"&&e.icon.wrap?(a(),c("div",Fo,[m(X,{image:e.icon,alt:e.icon.alt,height:e.icon.height||48,width:e.icon.width||48},null,8,["image","alt","height","width"])])):typeof e.icon=="object"?(a(),$(X,{key:1,image:e.icon,alt:e.icon.alt,height:e.icon.height||48,width:e.icon.width||48},null,8,["image","alt","height","width"])):e.icon?(a(),c("div",{key:2,class:"icon",innerHTML:e.icon},null,8,Do)):f("",!0),v("h2",{class:"title",innerHTML:e.title},null,8,Oo),e.details?(a(),c("p",{key:3,class:"details",innerHTML:e.details},null,8,Uo)):f("",!0),e.linkText?(a(),c("div",Go,[v("p",jo,[D(I(e.linkText)+" ",1),zo])])):f("",!0)])]),_:1},8,["href","rel","target","tag"]))}}),Ko=b(qo,[["__scopeId","data-v-bd37d1a2"]]),Wo={key:0,class:"VPFeatures"},Ro={class:"container"},Jo={class:"items"},Yo=_({__name:"VPFeatures",props:{features:{}},setup(o){const e=o,t=y(()=>{const n=e.features.length;if(n){if(n===2)return"grid-2";if(n===3)return"grid-3";if(n%3===0)return"grid-6";if(n>3)return"grid-4"}else return});return(n,s)=>n.features?(a(),c("div",Wo,[v("div",Ro,[v("div",Jo,[(a(!0),c(M,null,E(n.features,i=>(a(),c("div",{key:i.title,class:N(["item",[t.value]])},[m(Ko,{icon:i.icon,title:i.title,details:i.details,link:i.link,"link-text":i.linkText,rel:i.rel,target:i.target},null,8,["icon","title","details","link","link-text","rel","target"])],2))),128))])])])):f("",!0)}}),Qo=b(Yo,[["__scopeId","data-v-b1eea84a"]]),Xo=_({__name:"VPHomeFeatures",setup(o){const{frontmatter:e}=P();return(t,n)=>r(e).features?(a(),$(Qo,{key:0,class:"VPHomeFeatures",features:r(e).features},null,8,["features"])):f("",!0)}}),Zo=_({__name:"VPHomeContent",setup(o){const{width:e}=Ke({initialWidth:0,includeScrollbar:!1});return(t,n)=>(a(),c("div",{class:"vp-doc container",style:Ve(r(e)?{"--vp-offset":`calc(50% - ${r(e)/2}px)`}:{})},[l(t.$slots,"default",{},void 0,!0)],4))}}),xo=b(Zo,[["__scopeId","data-v-c141a4bd"]]),es={class:"VPHome"},ts=_({__name:"VPHome",setup(o){const{frontmatter:e}=P();return(t,n)=>{const s=q("Content");return a(),c("div",es,[l(t.$slots,"home-hero-before",{},void 0,!0),m(Bo,null,{"home-hero-info-before":d(()=>[l(t.$slots,"home-hero-info-before",{},void 0,!0)]),"home-hero-info":d(()=>[l(t.$slots,"home-hero-info",{},void 0,!0)]),"home-hero-info-after":d(()=>[l(t.$slots,"home-hero-info-after",{},void 0,!0)]),"home-hero-actions-after":d(()=>[l(t.$slots,"home-hero-actions-after",{},void 0,!0)]),"home-hero-image":d(()=>[l(t.$slots,"home-hero-image",{},void 0,!0)]),_:3}),l(t.$slots,"home-hero-after",{},void 0,!0),l(t.$slots,"home-features-before",{},void 0,!0),m(Xo),l(t.$slots,"home-features-after",{},void 0,!0),r(e).markdownStyles!==!1?(a(),$(xo,{key:0},{default:d(()=>[m(s)]),_:1})):(a(),$(s,{key:1}))])}}}),os=b(ts,[["__scopeId","data-v-07b1ad08"]]),ss={},ns={class:"VPPage"};function as(o,e){const t=q("Content");return a(),c("div",ns,[l(o.$slots,"page-top"),m(t),l(o.$slots,"page-bottom")])}const rs=b(ss,[["render",as]]),is=_({__name:"VPContent",setup(o){const{page:e,frontmatter:t}=P(),{hasSidebar:n}=O();return(s,i)=>(a(),c("div",{class:N(["VPContent",{"has-sidebar":r(n),"is-home":r(t).layout==="home"}]),id:"VPContent"},[r(e).isNotFound?l(s.$slots,"not-found",{key:0},()=>[m(ft)],!0):r(t).layout==="page"?(a(),$(rs,{key:1},{"page-top":d(()=>[l(s.$slots,"page-top",{},void 0,!0)]),"page-bottom":d(()=>[l(s.$slots,"page-bottom",{},void 0,!0)]),_:3})):r(t).layout==="home"?(a(),$(os,{key:2},{"home-hero-before":d(()=>[l(s.$slots,"home-hero-before",{},void 0,!0)]),"home-hero-info-before":d(()=>[l(s.$slots,"home-hero-info-before",{},void 0,!0)]),"home-hero-info":d(()=>[l(s.$slots,"home-hero-info",{},void 0,!0)]),"home-hero-info-after":d(()=>[l(s.$slots,"home-hero-info-after",{},void 0,!0)]),"home-hero-actions-after":d(()=>[l(s.$slots,"home-hero-actions-after",{},void 0,!0)]),"home-hero-image":d(()=>[l(s.$slots,"home-hero-image",{},void 0,!0)]),"home-hero-after":d(()=>[l(s.$slots,"home-hero-after",{},void 0,!0)]),"home-features-before":d(()=>[l(s.$slots,"home-features-before",{},void 0,!0)]),"home-features-after":d(()=>[l(s.$slots,"home-features-after",{},void 0,!0)]),_:3})):r(t).layout&&r(t).layout!=="doc"?(a(),$(W(r(t).layout),{key:3})):(a(),$(_o,{key:4},{"doc-top":d(()=>[l(s.$slots,"doc-top",{},void 0,!0)]),"doc-bottom":d(()=>[l(s.$slots,"doc-bottom",{},void 0,!0)]),"doc-footer-before":d(()=>[l(s.$slots,"doc-footer-before",{},void 0,!0)]),"doc-before":d(()=>[l(s.$slots,"doc-before",{},void 0,!0)]),"doc-after":d(()=>[l(s.$slots,"doc-after",{},void 0,!0)]),"aside-top":d(()=>[l(s.$slots,"aside-top",{},void 0,!0)]),"aside-outline-before":d(()=>[l(s.$slots,"aside-outline-before",{},void 0,!0)]),"aside-outline-after":d(()=>[l(s.$slots,"aside-outline-after",{},void 0,!0)]),"aside-ads-before":d(()=>[l(s.$slots,"aside-ads-before",{},void 0,!0)]),"aside-ads-after":d(()=>[l(s.$slots,"aside-ads-after",{},void 0,!0)]),"aside-bottom":d(()=>[l(s.$slots,"aside-bottom",{},void 0,!0)]),_:3}))],2))}}),ls=b(is,[["__scopeId","data-v-9a6c75ad"]]),cs={class:"container"},us=["innerHTML"],ds=["innerHTML"],vs=_({__name:"VPFooter",setup(o){const{theme:e,frontmatter:t}=P(),{hasSidebar:n}=O();return(s,i)=>r(e).footer&&r(t).footer!==!1?(a(),c("footer",{key:0,class:N(["VPFooter",{"has-sidebar":r(n)}])},[v("div",cs,[r(e).footer.message?(a(),c("p",{key:0,class:"message",innerHTML:r(e).footer.message},null,8,us)):f("",!0),r(e).footer.copyright?(a(),c("p",{key:1,class:"copyright",innerHTML:r(e).footer.copyright},null,8,ds)):f("",!0)])],2)):f("",!0)}}),ps=b(vs,[["__scopeId","data-v-566314d4"]]);function hs(){const{theme:o,frontmatter:e}=P(),t=Pe([]),n=y(()=>t.value.length>0);return x(()=>{t.value=_e(e.value.outline??o.value.outline)}),{headers:t,hasLocalNav:n}}const fs=o=>(B("data-v-883964e0"),o=o(),H(),o),_s={class:"menu-text"},ms=fs(()=>v("span",{class:"vpi-chevron-right icon"},null,-1)),ks={class:"header"},bs={class:"outline"},$s=_({__name:"VPLocalNavOutlineDropdown",props:{headers:{},navHeight:{}},setup(o){const e=o,{theme:t}=P(),n=w(!1),s=w(0),i=w(),u=w();function h(k){var V;(V=i.value)!=null&&V.contains(k.target)||(n.value=!1)}j(n,k=>{if(k){document.addEventListener("click",h);return}document.removeEventListener("click",h)}),le("Escape",()=>{n.value=!1}),x(()=>{n.value=!1});function p(){n.value=!n.value,s.value=window.innerHeight+Math.min(window.scrollY-e.navHeight,0)}function g(k){k.target.classList.contains("outline-link")&&(u.value&&(u.value.style.transition="none"),We(()=>{n.value=!1}))}function L(){n.value=!1,window.scrollTo({top:0,left:0,behavior:"smooth"})}return(k,V)=>(a(),c("div",{class:"VPLocalNavOutlineDropdown",style:Ve({"--vp-vh":s.value+"px"}),ref_key:"main",ref:i},[k.headers.length>0?(a(),c("button",{key:0,onClick:p,class:N({open:n.value})},[v("span",_s,I(r(Ne)(r(t))),1),ms],2)):(a(),c("button",{key:1,onClick:L},I(r(t).returnToTopLabel||"Return to top"),1)),m(ve,{name:"flyout"},{default:d(()=>[n.value?(a(),c("div",{key:0,ref_key:"items",ref:u,class:"items",onClick:g},[v("div",ks,[v("a",{class:"top-link",href:"#",onClick:L},I(r(t).returnToTopLabel||"Return to top"),1)]),v("div",bs,[m(Me,{headers:k.headers},null,8,["headers"])])],512)):f("",!0)]),_:1})],4))}}),gs=b($s,[["__scopeId","data-v-883964e0"]]),ys=o=>(B("data-v-2488c25a"),o=o(),H(),o),Ps={class:"container"},Ls=["aria-expanded"],Vs=ys(()=>v("span",{class:"vpi-align-left menu-icon"},null,-1)),Ss={class:"menu-text"},Ts=_({__name:"VPLocalNav",props:{open:{type:Boolean}},emits:["open-menu"],setup(o){const{theme:e,frontmatter:t}=P(),{hasSidebar:n}=O(),{headers:s}=hs(),{y:i}=Se(),u=w(0);R(()=>{u.value=parseInt(getComputedStyle(document.documentElement).getPropertyValue("--vp-nav-height"))}),x(()=>{s.value=_e(t.value.outline??e.value.outline)});const h=y(()=>s.value.length===0),p=y(()=>h.value&&!n.value),g=y(()=>({VPLocalNav:!0,"has-sidebar":n.value,empty:h.value,fixed:p.value}));return(L,k)=>r(t).layout!=="home"&&(!p.value||r(i)>=u.value)?(a(),c("div",{key:0,class:N(g.value)},[v("div",Ps,[r(n)?(a(),c("button",{key:0,class:"menu","aria-expanded":L.open,"aria-controls":"VPSidebarNav",onClick:k[0]||(k[0]=V=>L.$emit("open-menu"))},[Vs,v("span",Ss,I(r(e).sidebarMenuLabel||"Menu"),1)],8,Ls)):f("",!0),m(gs,{headers:r(s),navHeight:u.value},null,8,["headers","navHeight"])])],2)):f("",!0)}}),Is=b(Ts,[["__scopeId","data-v-2488c25a"]]);function ws(){const o=w(!1);function e(){o.value=!0,window.addEventListener("resize",s)}function t(){o.value=!1,window.removeEventListener("resize",s)}function n(){o.value?t():e()}function s(){window.outerWidth>=768&&t()}const i=ee();return j(()=>i.path,t),{isScreenOpen:o,openScreen:e,closeScreen:t,toggleScreen:n}}const Ns={},Ms={class:"VPSwitch",type:"button",role:"switch"},As={class:"check"},Cs={key:0,class:"icon"};function Bs(o,e){return a(),c("button",Ms,[v("span",As,[o.$slots.default?(a(),c("span",Cs,[l(o.$slots,"default",{},void 0,!0)])):f("",!0)])])}const Hs=b(Ns,[["render",Bs],["__scopeId","data-v-b4ccac88"]]),Ce=o=>(B("data-v-7df97737"),o=o(),H(),o),Es=Ce(()=>v("span",{class:"vpi-sun sun"},null,-1)),Fs=Ce(()=>v("span",{class:"vpi-moon moon"},null,-1)),Ds=_({__name:"VPSwitchAppearance",setup(o){const{isDark:e,theme:t}=P(),n=te("toggle-appearance",()=>{e.value=!e.value}),s=y(()=>e.value?t.value.lightModeSwitchTitle||"Switch to light theme":t.value.darkModeSwitchTitle||"Switch to dark theme");return(i,u)=>(a(),$(Hs,{title:s.value,class:"VPSwitchAppearance","aria-checked":r(e),onClick:r(n)},{default:d(()=>[Es,Fs]),_:1},8,["title","aria-checked","onClick"]))}}),me=b(Ds,[["__scopeId","data-v-7df97737"]]),Os={key:0,class:"VPNavBarAppearance"},Us=_({__name:"VPNavBarAppearance",setup(o){const{site:e}=P();return(t,n)=>r(e).appearance&&r(e).appearance!=="force-dark"?(a(),c("div",Os,[m(me)])):f("",!0)}}),Gs=b(Us,[["__scopeId","data-v-283b26e9"]]),ke=w();let Be=!1,re=0;function js(o){const e=w(!1);if(oe){!Be&&zs(),re++;const t=j(ke,n=>{var s,i,u;n===o.el.value||(s=o.el.value)!=null&&s.contains(n)?(e.value=!0,(i=o.onFocus)==null||i.call(o)):(e.value=!1,(u=o.onBlur)==null||u.call(o))});he(()=>{t(),re--,re||qs()})}return Re(e)}function zs(){document.addEventListener("focusin",He),Be=!0,ke.value=document.activeElement}function qs(){document.removeEventListener("focusin",He)}function He(){ke.value=document.activeElement}const Ks={class:"VPMenuLink"},Ws=_({__name:"VPMenuLink",props:{item:{}},setup(o){const{page:e}=P();return(t,n)=>(a(),c("div",Ks,[m(F,{class:N({active:r(z)(r(e).relativePath,t.item.activeMatch||t.item.link,!!t.item.activeMatch)}),href:t.item.link,target:t.item.target,rel:t.item.rel},{default:d(()=>[D(I(t.item.text),1)]),_:1},8,["class","href","target","rel"])]))}}),se=b(Ws,[["__scopeId","data-v-f51f088d"]]),Rs={class:"VPMenuGroup"},Js={key:0,class:"title"},Ys=_({__name:"VPMenuGroup",props:{text:{},items:{}},setup(o){return(e,t)=>(a(),c("div",Rs,[e.text?(a(),c("p",Js,I(e.text),1)):f("",!0),(a(!0),c(M,null,E(e.items,n=>(a(),c(M,null,["link"in n?(a(),$(se,{key:0,item:n},null,8,["item"])):f("",!0)],64))),256))]))}}),Qs=b(Ys,[["__scopeId","data-v-a6b0397c"]]),Xs={class:"VPMenu"},Zs={key:0,class:"items"},xs=_({__name:"VPMenu",props:{items:{}},setup(o){return(e,t)=>(a(),c("div",Xs,[e.items?(a(),c("div",Zs,[(a(!0),c(M,null,E(e.items,n=>(a(),c(M,{key:n.text},["link"in n?(a(),$(se,{key:0,item:n},null,8,["item"])):(a(),$(Qs,{key:1,text:n.text,items:n.items},null,8,["text","items"]))],64))),128))])):f("",!0),l(e.$slots,"default",{},void 0,!0)]))}}),en=b(xs,[["__scopeId","data-v-e42ed9b3"]]),tn=o=>(B("data-v-af5898d3"),o=o(),H(),o),on=["aria-expanded","aria-label"],sn={key:0,class:"text"},nn=["innerHTML"],an=tn(()=>v("span",{class:"vpi-chevron-down text-icon"},null,-1)),rn={key:1,class:"vpi-more-horizontal icon"},ln={class:"menu"},cn=_({__name:"VPFlyout",props:{icon:{},button:{},label:{},items:{}},setup(o){const e=w(!1),t=w();js({el:t,onBlur:n});function n(){e.value=!1}return(s,i)=>(a(),c("div",{class:"VPFlyout",ref_key:"el",ref:t,onMouseenter:i[1]||(i[1]=u=>e.value=!0),onMouseleave:i[2]||(i[2]=u=>e.value=!1)},[v("button",{type:"button",class:"button","aria-haspopup":"true","aria-expanded":e.value,"aria-label":s.label,onClick:i[0]||(i[0]=u=>e.value=!e.value)},[s.button||s.icon?(a(),c("span",sn,[s.icon?(a(),c("span",{key:0,class:N([s.icon,"option-icon"])},null,2)):f("",!0),s.button?(a(),c("span",{key:1,innerHTML:s.button},null,8,nn)):f("",!0),an])):(a(),c("span",rn))],8,on),v("div",ln,[m(en,{items:s.items},{default:d(()=>[l(s.$slots,"default",{},void 0,!0)]),_:3},8,["items"])])],544))}}),be=b(cn,[["__scopeId","data-v-af5898d3"]]),un=["href","aria-label","innerHTML"],dn=_({__name:"VPSocialLink",props:{icon:{},link:{},ariaLabel:{}},setup(o){const e=o,t=y(()=>typeof e.icon=="object"?e.icon.svg:`<span class="vpi-social-${e.icon}" />`);return(n,s)=>(a(),c("a",{class:"VPSocialLink no-icon",href:n.link,"aria-label":n.ariaLabel??(typeof n.icon=="string"?n.icon:""),target:"_blank",rel:"noopener",innerHTML:t.value},null,8,un))}}),vn=b(dn,[["__scopeId","data-v-358b6670"]]),pn={class:"VPSocialLinks"},hn=_({__name:"VPSocialLinks",props:{links:{}},setup(o){return(e,t)=>(a(),c("div",pn,[(a(!0),c(M,null,E(e.links,({link:n,icon:s,ariaLabel:i})=>(a(),$(vn,{key:n,icon:s,link:n,ariaLabel:i},null,8,["icon","link","ariaLabel"]))),128))]))}}),$e=b(hn,[["__scopeId","data-v-e71e869c"]]),fn={key:0,class:"group translations"},_n={class:"trans-title"},mn={key:1,class:"group"},kn={class:"item appearance"},bn={class:"label"},$n={class:"appearance-action"},gn={key:2,class:"group"},yn={class:"item social-links"},Pn=_({__name:"VPNavBarExtra",setup(o){const{site:e,theme:t}=P(),{localeLinks:n,currentLang:s}=J({correspondingLink:!0}),i=y(()=>n.value.length&&s.value.label||e.value.appearance||t.value.socialLinks);return(u,h)=>i.value?(a(),$(be,{key:0,class:"VPNavBarExtra",label:"extra navigation"},{default:d(()=>[r(n).length&&r(s).label?(a(),c("div",fn,[v("p",_n,I(r(s).label),1),(a(!0),c(M,null,E(r(n),p=>(a(),$(se,{key:p.link,item:p},null,8,["item"]))),128))])):f("",!0),r(e).appearance&&r(e).appearance!=="force-dark"?(a(),c("div",mn,[v("div",kn,[v("p",bn,I(r(t).darkModeSwitchLabel||"Appearance"),1),v("div",$n,[m(me)])])])):f("",!0),r(t).socialLinks?(a(),c("div",gn,[v("div",yn,[m($e,{class:"social-links-list",links:r(t).socialLinks},null,8,["links"])])])):f("",!0)]),_:1})):f("",!0)}}),Ln=b(Pn,[["__scopeId","data-v-8e87c032"]]),Vn=o=>(B("data-v-6bee1efd"),o=o(),H(),o),Sn=["aria-expanded"],Tn=Vn(()=>v("span",{class:"container"},[v("span",{class:"top"}),v("span",{class:"middle"}),v("span",{class:"bottom"})],-1)),In=[Tn],wn=_({__name:"VPNavBarHamburger",props:{active:{type:Boolean}},emits:["click"],setup(o){return(e,t)=>(a(),c("button",{type:"button",class:N(["VPNavBarHamburger",{active:e.active}]),"aria-label":"mobile navigation","aria-expanded":e.active,"aria-controls":"VPNavScreen",onClick:t[0]||(t[0]=n=>e.$emit("click"))},In,10,Sn))}}),Nn=b(wn,[["__scopeId","data-v-6bee1efd"]]),Mn=["innerHTML"],An=_({__name:"VPNavBarMenuLink",props:{item:{}},setup(o){const{page:e}=P();return(t,n)=>(a(),$(F,{class:N({VPNavBarMenuLink:!0,active:r(z)(r(e).relativePath,t.item.activeMatch||t.item.link,!!t.item.activeMatch)}),href:t.item.link,noIcon:t.item.noIcon,target:t.item.target,rel:t.item.rel,tabindex:"0"},{default:d(()=>[v("span",{innerHTML:t.item.text},null,8,Mn)]),_:1},8,["class","href","noIcon","target","rel"]))}}),Cn=b(An,[["__scopeId","data-v-08fbf4b6"]]),Bn=_({__name:"VPNavBarMenuGroup",props:{item:{}},setup(o){const e=o,{page:t}=P(),n=i=>"link"in i?z(t.value.relativePath,i.link,!!e.item.activeMatch):i.items.some(n),s=y(()=>n(e.item));return(i,u)=>(a(),$(be,{class:N({VPNavBarMenuGroup:!0,active:r(z)(r(t).relativePath,i.item.activeMatch,!!i.item.activeMatch)||s.value}),button:i.item.text,items:i.item.items},null,8,["class","button","items"]))}}),Hn=o=>(B("data-v-f732b5d0"),o=o(),H(),o),En={key:0,"aria-labelledby":"main-nav-aria-label",class:"VPNavBarMenu"},Fn=Hn(()=>v("span",{id:"main-nav-aria-label",class:"visually-hidden"},"Main Navigation",-1)),Dn=_({__name:"VPNavBarMenu",setup(o){const{theme:e}=P();return(t,n)=>r(e).nav?(a(),c("nav",En,[Fn,(a(!0),c(M,null,E(r(e).nav,s=>(a(),c(M,{key:s.text},["link"in s?(a(),$(Cn,{key:0,item:s},null,8,["item"])):(a(),$(Bn,{key:1,item:s},null,8,["item"]))],64))),128))])):f("",!0)}}),On=b(Dn,[["__scopeId","data-v-f732b5d0"]]);function Un(o){const{localeIndex:e,theme:t}=P();function n(s){var A,C,S;const i=s.split("."),u=(A=t.value.search)==null?void 0:A.options,h=u&&typeof u=="object",p=h&&((S=(C=u.locales)==null?void 0:C[e.value])==null?void 0:S.translations)||null,g=h&&u.translations||null;let L=p,k=g,V=o;const T=i.pop();for(const U of i){let G=null;const K=V==null?void 0:V[U];K&&(G=V=K);const ne=k==null?void 0:k[U];ne&&(G=k=ne);const ae=L==null?void 0:L[U];ae&&(G=L=ae),K||(V=G),ne||(k=G),ae||(L=G)}return(L==null?void 0:L[T])??(k==null?void 0:k[T])??(V==null?void 0:V[T])??""}return n}const Gn=["aria-label"],jn={class:"DocSearch-Button-Container"},zn=v("span",{class:"vp-icon DocSearch-Search-Icon"},null,-1),qn={class:"DocSearch-Button-Placeholder"},Kn=v("span",{class:"DocSearch-Button-Keys"},[v("kbd",{class:"DocSearch-Button-Key"}),v("kbd",{class:"DocSearch-Button-Key"},"K")],-1),ge=_({__name:"VPNavBarSearchButton",setup(o){const t=Un({button:{buttonText:"Search",buttonAriaLabel:"Search"}});return(n,s)=>(a(),c("button",{type:"button",class:"DocSearch DocSearch-Button","aria-label":r(t)("button.buttonAriaLabel")},[v("span",jn,[zn,v("span",qn,I(r(t)("button.buttonText")),1)]),Kn],8,Gn))}}),Wn={class:"VPNavBarSearch"},Rn={id:"local-search"},Jn={key:1,id:"docsearch"},Yn=_({__name:"VPNavBarSearch",setup(o){const e=Je(()=>Ye(()=>import("./VPLocalSearchBox.DIR2B3hV.js"),__vite__mapDeps([0,1]))),t=()=>null,{theme:n}=P(),s=w(!1),i=w(!1);R(()=>{});function u(){s.value||(s.value=!0,setTimeout(h,16))}function h(){const k=new Event("keydown");k.key="k",k.metaKey=!0,window.dispatchEvent(k),setTimeout(()=>{document.querySelector(".DocSearch-Modal")||h()},16)}function p(k){const V=k.target,T=V.tagName;return V.isContentEditable||T==="INPUT"||T==="SELECT"||T==="TEXTAREA"}const g=w(!1);le("k",k=>{(k.ctrlKey||k.metaKey)&&(k.preventDefault(),g.value=!0)}),le("/",k=>{p(k)||(k.preventDefault(),g.value=!0)});const L="local";return(k,V)=>{var T;return a(),c("div",Wn,[r(L)==="local"?(a(),c(M,{key:0},[g.value?(a(),$(r(e),{key:0,onClose:V[0]||(V[0]=A=>g.value=!1)})):f("",!0),v("div",Rn,[m(ge,{onClick:V[1]||(V[1]=A=>g.value=!0)})])],64)):r(L)==="algolia"?(a(),c(M,{key:1},[s.value?(a(),$(r(t),{key:0,algolia:((T=r(n).search)==null?void 0:T.options)??r(n).algolia,onVnodeBeforeMount:V[2]||(V[2]=A=>i.value=!0)},null,8,["algolia"])):f("",!0),i.value?f("",!0):(a(),c("div",Jn,[m(ge,{onClick:u})]))],64)):f("",!0)])}}}),Qn=_({__name:"VPNavBarSocialLinks",setup(o){const{theme:e}=P();return(t,n)=>r(e).socialLinks?(a(),$($e,{key:0,class:"VPNavBarSocialLinks",links:r(e).socialLinks},null,8,["links"])):f("",!0)}}),Xn=b(Qn,[["__scopeId","data-v-ef6192dc"]]),Zn=["href","rel","target"],xn={key:1},ea={key:2},ta=_({__name:"VPNavBarTitle",setup(o){const{site:e,theme:t}=P(),{hasSidebar:n}=O(),{currentLang:s}=J(),i=y(()=>{var p;return typeof t.value.logoLink=="string"?t.value.logoLink:(p=t.value.logoLink)==null?void 0:p.link}),u=y(()=>{var p;return typeof t.value.logoLink=="string"||(p=t.value.logoLink)==null?void 0:p.rel}),h=y(()=>{var p;return typeof t.value.logoLink=="string"||(p=t.value.logoLink)==null?void 0:p.target});return(p,g)=>(a(),c("div",{class:N(["VPNavBarTitle",{"has-sidebar":r(n)}])},[v("a",{class:"title",href:i.value??r(fe)(r(s).link),rel:u.value,target:h.value},[l(p.$slots,"nav-bar-title-before",{},void 0,!0),r(t).logo?(a(),$(X,{key:0,class:"logo",image:r(t).logo},null,8,["image"])):f("",!0),r(t).siteTitle?(a(),c("span",xn,I(r(t).siteTitle),1)):r(t).siteTitle===void 0?(a(),c("span",ea,I(r(e).title),1)):f("",!0),l(p.$slots,"nav-bar-title-after",{},void 0,!0)],8,Zn)],2))}}),oa=b(ta,[["__scopeId","data-v-0ad69264"]]),sa={class:"items"},na={class:"title"},aa=_({__name:"VPNavBarTranslations",setup(o){const{theme:e}=P(),{localeLinks:t,currentLang:n}=J({correspondingLink:!0});return(s,i)=>r(t).length&&r(n).label?(a(),$(be,{key:0,class:"VPNavBarTranslations",icon:"vpi-languages",label:r(e).langMenuLabel||"Change language"},{default:d(()=>[v("div",sa,[v("p",na,I(r(n).label),1),(a(!0),c(M,null,E(r(t),u=>(a(),$(se,{key:u.link,item:u},null,8,["item"]))),128))])]),_:1},8,["label"])):f("",!0)}}),ra=b(aa,[["__scopeId","data-v-acee064b"]]),ia=o=>(B("data-v-844edcde"),o=o(),H(),o),la={class:"wrapper"},ca={class:"container"},ua={class:"title"},da={class:"content"},va={class:"content-body"},pa=ia(()=>v("div",{class:"divider"},[v("div",{class:"divider-line"})],-1)),ha=_({__name:"VPNavBar",props:{isScreenOpen:{type:Boolean}},emits:["toggle-screen"],setup(o){const{y:e}=Se(),{hasSidebar:t}=O(),{frontmatter:n}=P(),s=w({});return ye(()=>{s.value={"has-sidebar":t.value,home:n.value.layout==="home",top:e.value===0}}),(i,u)=>(a(),c("div",{class:N(["VPNavBar",s.value])},[v("div",la,[v("div",ca,[v("div",ua,[m(oa,null,{"nav-bar-title-before":d(()=>[l(i.$slots,"nav-bar-title-before",{},void 0,!0)]),"nav-bar-title-after":d(()=>[l(i.$slots,"nav-bar-title-after",{},void 0,!0)]),_:3})]),v("div",da,[v("div",va,[l(i.$slots,"nav-bar-content-before",{},void 0,!0),m(Yn,{class:"search"}),m(On,{class:"menu"}),m(ra,{class:"translations"}),m(Gs,{class:"appearance"}),m(Xn,{class:"social-links"}),m(Ln,{class:"extra"}),l(i.$slots,"nav-bar-content-after",{},void 0,!0),m(Nn,{class:"hamburger",active:i.isScreenOpen,onClick:u[0]||(u[0]=h=>i.$emit("toggle-screen"))},null,8,["active"])])])])]),pa],2))}}),fa=b(ha,[["__scopeId","data-v-844edcde"]]),_a={key:0,class:"VPNavScreenAppearance"},ma={class:"text"},ka=_({__name:"VPNavScreenAppearance",setup(o){const{site:e,theme:t}=P();return(n,s)=>r(e).appearance&&r(e).appearance!=="force-dark"?(a(),c("div",_a,[v("p",ma,I(r(t).darkModeSwitchLabel||"Appearance"),1),m(me)])):f("",!0)}}),ba=b(ka,[["__scopeId","data-v-338d9b48"]]),$a=_({__name:"VPNavScreenMenuLink",props:{item:{}},setup(o){const e=te("close-screen");return(t,n)=>(a(),$(F,{class:"VPNavScreenMenuLink",href:t.item.link,target:t.item.target,rel:t.item.rel,onClick:r(e),innerHTML:t.item.text},null,8,["href","target","rel","onClick","innerHTML"]))}}),ga=b($a,[["__scopeId","data-v-1a934d60"]]),ya=_({__name:"VPNavScreenMenuGroupLink",props:{item:{}},setup(o){const e=te("close-screen");return(t,n)=>(a(),$(F,{class:"VPNavScreenMenuGroupLink",href:t.item.link,target:t.item.target,rel:t.item.rel,onClick:r(e)},{default:d(()=>[D(I(t.item.text),1)]),_:1},8,["href","target","rel","onClick"]))}}),Ee=b(ya,[["__scopeId","data-v-aea78dd1"]]),Pa={class:"VPNavScreenMenuGroupSection"},La={key:0,class:"title"},Va=_({__name:"VPNavScreenMenuGroupSection",props:{text:{},items:{}},setup(o){return(e,t)=>(a(),c("div",Pa,[e.text?(a(),c("p",La,I(e.text),1)):f("",!0),(a(!0),c(M,null,E(e.items,n=>(a(),$(Ee,{key:n.text,item:n},null,8,["item"]))),128))]))}}),Sa=b(Va,[["__scopeId","data-v-f60dbfa7"]]),Ta=o=>(B("data-v-d2212c70"),o=o(),H(),o),Ia=["aria-controls","aria-expanded"],wa=["innerHTML"],Na=Ta(()=>v("span",{class:"vpi-plus button-icon"},null,-1)),Ma=["id"],Aa={key:1,class:"group"},Ca=_({__name:"VPNavScreenMenuGroup",props:{text:{},items:{}},setup(o){const e=o,t=w(!1),n=y(()=>`NavScreenGroup-${e.text.replace(" ","-").toLowerCase()}`);function s(){t.value=!t.value}return(i,u)=>(a(),c("div",{class:N(["VPNavScreenMenuGroup",{open:t.value}])},[v("button",{class:"button","aria-controls":n.value,"aria-expanded":t.value,onClick:s},[v("span",{class:"button-text",innerHTML:i.text},null,8,wa),Na],8,Ia),v("div",{id:n.value,class:"items"},[(a(!0),c(M,null,E(i.items,h=>(a(),c(M,{key:h.text},["link"in h?(a(),c("div",{key:h.text,class:"item"},[m(Ee,{item:h},null,8,["item"])])):(a(),c("div",Aa,[m(Sa,{text:h.text,items:h.items},null,8,["text","items"])]))],64))),128))],8,Ma)],2))}}),Ba=b(Ca,[["__scopeId","data-v-d2212c70"]]),Ha={key:0,class:"VPNavScreenMenu"},Ea=_({__name:"VPNavScreenMenu",setup(o){const{theme:e}=P();return(t,n)=>r(e).nav?(a(),c("nav",Ha,[(a(!0),c(M,null,E(r(e).nav,s=>(a(),c(M,{key:s.text},["link"in s?(a(),$(ga,{key:0,item:s},null,8,["item"])):(a(),$(Ba,{key:1,text:s.text||"",items:s.items},null,8,["text","items"]))],64))),128))])):f("",!0)}}),Fa=_({__name:"VPNavScreenSocialLinks",setup(o){const{theme:e}=P();return(t,n)=>r(e).socialLinks?(a(),$($e,{key:0,class:"VPNavScreenSocialLinks",links:r(e).socialLinks},null,8,["links"])):f("",!0)}}),Fe=o=>(B("data-v-516e4bc3"),o=o(),H(),o),Da=Fe(()=>v("span",{class:"vpi-languages icon lang"},null,-1)),Oa=Fe(()=>v("span",{class:"vpi-chevron-down icon chevron"},null,-1)),Ua={class:"list"},Ga=_({__name:"VPNavScreenTranslations",setup(o){const{localeLinks:e,currentLang:t}=J({correspondingLink:!0}),n=w(!1);function s(){n.value=!n.value}return(i,u)=>r(e).length&&r(t).label?(a(),c("div",{key:0,class:N(["VPNavScreenTranslations",{open:n.value}])},[v("button",{class:"title",onClick:s},[Da,D(" "+I(r(t).label)+" ",1),Oa]),v("ul",Ua,[(a(!0),c(M,null,E(r(e),h=>(a(),c("li",{key:h.link,class:"item"},[m(F,{class:"link",href:h.link},{default:d(()=>[D(I(h.text),1)]),_:2},1032,["href"])]))),128))])],2)):f("",!0)}}),ja=b(Ga,[["__scopeId","data-v-516e4bc3"]]),za={class:"container"},qa=_({__name:"VPNavScreen",props:{open:{type:Boolean}},setup(o){const e=w(null),t=Te(oe?document.body:null);return(n,s)=>(a(),$(ve,{name:"fade",onEnter:s[0]||(s[0]=i=>t.value=!0),onAfterLeave:s[1]||(s[1]=i=>t.value=!1)},{default:d(()=>[n.open?(a(),c("div",{key:0,class:"VPNavScreen",ref_key:"screen",ref:e,id:"VPNavScreen"},[v("div",za,[l(n.$slots,"nav-screen-content-before",{},void 0,!0),m(Ea,{class:"menu"}),m(ja,{class:"translations"}),m(ba,{class:"appearance"}),m(Fa,{class:"social-links"}),l(n.$slots,"nav-screen-content-after",{},void 0,!0)])],512)):f("",!0)]),_:3}))}}),Ka=b(qa,[["__scopeId","data-v-57cce842"]]),Wa={key:0,class:"VPNav"},Ra=_({__name:"VPNav",setup(o){const{isScreenOpen:e,closeScreen:t,toggleScreen:n}=ws(),{frontmatter:s}=P(),i=y(()=>s.value.navbar!==!1);return Ie("close-screen",t),Z(()=>{oe&&document.documentElement.classList.toggle("hide-nav",!i.value)}),(u,h)=>i.value?(a(),c("header",Wa,[m(fa,{"is-screen-open":r(e),onToggleScreen:r(n)},{"nav-bar-title-before":d(()=>[l(u.$slots,"nav-bar-title-before",{},void 0,!0)]),"nav-bar-title-after":d(()=>[l(u.$slots,"nav-bar-title-after",{},void 0,!0)]),"nav-bar-content-before":d(()=>[l(u.$slots,"nav-bar-content-before",{},void 0,!0)]),"nav-bar-content-after":d(()=>[l(u.$slots,"nav-bar-content-after",{},void 0,!0)]),_:3},8,["is-screen-open","onToggleScreen"]),m(Ka,{open:r(e)},{"nav-screen-content-before":d(()=>[l(u.$slots,"nav-screen-content-before",{},void 0,!0)]),"nav-screen-content-after":d(()=>[l(u.$slots,"nav-screen-content-after",{},void 0,!0)]),_:3},8,["open"])])):f("",!0)}}),Ja=b(Ra,[["__scopeId","data-v-7ad780c2"]]),De=o=>(B("data-v-c24f735a"),o=o(),H(),o),Ya=["role","tabindex"],Qa=De(()=>v("div",{class:"indicator"},null,-1)),Xa=De(()=>v("span",{class:"vpi-chevron-right caret-icon"},null,-1)),Za=[Xa],xa={key:1,class:"items"},er=_({__name:"VPSidebarItem",props:{item:{},depth:{}},setup(o){const e=o,{collapsed:t,collapsible:n,isLink:s,isActiveLink:i,hasActiveLink:u,hasChildren:h,toggle:p}=bt(y(()=>e.item)),g=y(()=>h.value?"section":"div"),L=y(()=>s.value?"a":"div"),k=y(()=>h.value?e.depth+2===7?"p":`h${e.depth+2}`:"p"),V=y(()=>s.value?void 0:"button"),T=y(()=>[[`level-${e.depth}`],{collapsible:n.value},{collapsed:t.value},{"is-link":s.value},{"is-active":i.value},{"has-active":u.value}]);function A(S){"key"in S&&S.key!=="Enter"||!e.item.link&&p()}function C(){e.item.link&&p()}return(S,U)=>{const G=q("VPSidebarItem",!0);return a(),$(W(g.value),{class:N(["VPSidebarItem",T.value])},{default:d(()=>[S.item.text?(a(),c("div",Y({key:0,class:"item",role:V.value},Xe(S.item.items?{click:A,keydown:A}:{},!0),{tabindex:S.item.items&&0}),[Qa,S.item.link?(a(),$(F,{key:0,tag:L.value,class:"link",href:S.item.link,rel:S.item.rel,target:S.item.target},{default:d(()=>[(a(),$(W(k.value),{class:"text",innerHTML:S.item.text},null,8,["innerHTML"]))]),_:1},8,["tag","href","rel","target"])):(a(),$(W(k.value),{key:1,class:"text",innerHTML:S.item.text},null,8,["innerHTML"])),S.item.collapsed!=null&&S.item.items&&S.item.items.length?(a(),c("div",{key:2,class:"caret",role:"button","aria-label":"toggle section",onClick:C,onKeydown:Qe(C,["enter"]),tabindex:"0"},Za,32)):f("",!0)],16,Ya)):f("",!0),S.item.items&&S.item.items.length?(a(),c("div",xa,[S.depth<5?(a(!0),c(M,{key:0},E(S.item.items,K=>(a(),$(G,{key:K.text,item:K,depth:S.depth+1},null,8,["item","depth"]))),128)):f("",!0)])):f("",!0)]),_:1},8,["class"])}}}),tr=b(er,[["__scopeId","data-v-c24f735a"]]),Oe=o=>(B("data-v-4871f9f5"),o=o(),H(),o),or=Oe(()=>v("div",{class:"curtain"},null,-1)),sr={class:"nav",id:"VPSidebarNav","aria-labelledby":"sidebar-aria-label",tabindex:"-1"},nr=Oe(()=>v("span",{class:"visually-hidden",id:"sidebar-aria-label"}," Sidebar Navigation ",-1)),ar=_({__name:"VPSidebar",props:{open:{type:Boolean}},setup(o){const{sidebarGroups:e,hasSidebar:t}=O(),n=o,s=w(null),i=Te(oe?document.body:null);return j([n,s],()=>{var u;n.open?(i.value=!0,(u=s.value)==null||u.focus()):i.value=!1},{immediate:!0,flush:"post"}),(u,h)=>r(t)?(a(),c("aside",{key:0,class:N(["VPSidebar",{open:u.open}]),ref_key:"navEl",ref:s,onClick:h[0]||(h[0]=Ze(()=>{},["stop"]))},[or,v("nav",sr,[nr,l(u.$slots,"sidebar-nav-before",{},void 0,!0),(a(!0),c(M,null,E(r(e),p=>(a(),c("div",{key:p.text,class:"group"},[m(tr,{item:p,depth:0},null,8,["item"])]))),128)),l(u.$slots,"sidebar-nav-after",{},void 0,!0)])],2)):f("",!0)}}),rr=b(ar,[["__scopeId","data-v-4871f9f5"]]),ir=_({__name:"VPSkipLink",setup(o){const e=ee(),t=w();j(()=>e.path,()=>t.value.focus());function n({target:s}){const i=document.getElementById(decodeURIComponent(s.hash).slice(1));if(i){const u=()=>{i.removeAttribute("tabindex"),i.removeEventListener("blur",u)};i.setAttribute("tabindex","-1"),i.addEventListener("blur",u),i.focus(),window.scrollTo(0,0)}}return(s,i)=>(a(),c(M,null,[v("span",{ref_key:"backToTop",ref:t,tabindex:"-1"},null,512),v("a",{href:"#VPContent",class:"VPSkipLink visually-hidden",onClick:n}," Skip to content ")],64))}}),lr=b(ir,[["__scopeId","data-v-c8291ffa"]]),cr=_({__name:"Layout",setup(o){const{isOpen:e,open:t,close:n}=O(),s=ee();j(()=>s.path,n),kt(e,n);const{frontmatter:i}=P(),u=xe(),h=y(()=>!!u["home-hero-image"]);return Ie("hero-image-slot-exists",h),(p,g)=>{const L=q("Content");return r(i).layout!==!1?(a(),c("div",{key:0,class:N(["Layout",r(i).pageClass])},[l(p.$slots,"layout-top",{},void 0,!0),m(lr),m(st,{class:"backdrop",show:r(e),onClick:r(n)},null,8,["show","onClick"]),m(Ja,null,{"nav-bar-title-before":d(()=>[l(p.$slots,"nav-bar-title-before",{},void 0,!0)]),"nav-bar-title-after":d(()=>[l(p.$slots,"nav-bar-title-after",{},void 0,!0)]),"nav-bar-content-before":d(()=>[l(p.$slots,"nav-bar-content-before",{},void 0,!0)]),"nav-bar-content-after":d(()=>[l(p.$slots,"nav-bar-content-after",{},void 0,!0)]),"nav-screen-content-before":d(()=>[l(p.$slots,"nav-screen-content-before",{},void 0,!0)]),"nav-screen-content-after":d(()=>[l(p.$slots,"nav-screen-content-after",{},void 0,!0)]),_:3}),m(Is,{open:r(e),onOpenMenu:r(t)},null,8,["open","onOpenMenu"]),m(rr,{open:r(e)},{"sidebar-nav-before":d(()=>[l(p.$slots,"sidebar-nav-before",{},void 0,!0)]),"sidebar-nav-after":d(()=>[l(p.$slots,"sidebar-nav-after",{},void 0,!0)]),_:3},8,["open"]),m(ls,null,{"page-top":d(()=>[l(p.$slots,"page-top",{},void 0,!0)]),"page-bottom":d(()=>[l(p.$slots,"page-bottom",{},void 0,!0)]),"not-found":d(()=>[l(p.$slots,"not-found",{},void 0,!0)]),"home-hero-before":d(()=>[l(p.$slots,"home-hero-before",{},void 0,!0)]),"home-hero-info-before":d(()=>[l(p.$slots,"home-hero-info-before",{},void 0,!0)]),"home-hero-info":d(()=>[l(p.$slots,"home-hero-info",{},void 0,!0)]),"home-hero-info-after":d(()=>[l(p.$slots,"home-hero-info-after",{},void 0,!0)]),"home-hero-actions-after":d(()=>[l(p.$slots,"home-hero-actions-after",{},void 0,!0)]),"home-hero-image":d(()=>[l(p.$slots,"home-hero-image",{},void 0,!0)]),"home-hero-after":d(()=>[l(p.$slots,"home-hero-after",{},void 0,!0)]),"home-features-before":d(()=>[l(p.$slots,"home-features-before",{},void 0,!0)]),"home-features-after":d(()=>[l(p.$slots,"home-features-after",{},void 0,!0)]),"doc-footer-before":d(()=>[l(p.$slots,"doc-footer-before",{},void 0,!0)]),"doc-before":d(()=>[l(p.$slots,"doc-before",{},void 0,!0)]),"doc-after":d(()=>[l(p.$slots,"doc-after",{},void 0,!0)]),"doc-top":d(()=>[l(p.$slots,"doc-top",{},void 0,!0)]),"doc-bottom":d(()=>[l(p.$slots,"doc-bottom",{},void 0,!0)]),"aside-top":d(()=>[l(p.$slots,"aside-top",{},void 0,!0)]),"aside-bottom":d(()=>[l(p.$slots,"aside-bottom",{},void 0,!0)]),"aside-outline-before":d(()=>[l(p.$slots,"aside-outline-before",{},void 0,!0)]),"aside-outline-after":d(()=>[l(p.$slots,"aside-outline-after",{},void 0,!0)]),"aside-ads-before":d(()=>[l(p.$slots,"aside-ads-before",{},void 0,!0)]),"aside-ads-after":d(()=>[l(p.$slots,"aside-ads-after",{},void 0,!0)]),_:3}),m(ps),l(p.$slots,"layout-bottom",{},void 0,!0)],2)):(a(),$(L,{key:1}))}}}),ur=b(cr,[["__scopeId","data-v-d8b57b2d"]]),vr={Layout:ur,enhanceApp:({app:o})=>{o.component("Badge",et)}};export{Un as c,vr as t,P as u};
