import{_ as t,D as e,c as p,I as a,j as s,a as l,a3 as n,o as h}from"./chunks/framework.D42TvibZ.js";const W=JSON.parse('{"title":"V2接口 🅱️","description":"","frontmatter":{"title":"V2接口 🅱️"},"headers":[],"relativePath":"api-dock-v2.md","filePath":"api-dock-v2.md","lastUpdated":1740580269000}'),k={name:"api-dock-v2.md"},r=s("h1",{id:"lanzou-pro-v2-接口列表",tabindex:"-1"},[l("Lanzou Pro V2 接口列表 "),s("a",{class:"header-anchor",href:"#lanzou-pro-v2-接口列表","aria-label":'Permalink to "Lanzou Pro V2 接口列表"'},"​")],-1),o=s("hr",null,null,-1),d=s("p",null,"本项目基于 Python3.10 开发、Web框架选用 Flask4.0、文档生成工具 VitePress.",-1),E=s("thead",null,[s("tr",null,[s("th",null,"API"),s("th",{style:{"text-align":"center"}},"状态"),s("th",{style:{"text-align":"center"}},"版本"),s("th",{style:{"text-align":"left"}},"路由")])],-1),c=s("td",null,"获取文件与目录",-1),u=s("td",{style:{"text-align":"center"}},"✅",-1),g={style:{"text-align":"center"}},y=s("td",{style:{"text-align":"left"}},[s("code",null,"/v2/getFilesAndDirectories/{Lid}/{page}")],-1),b=s("td",null,"获取目录",-1),F=s("td",{style:{"text-align":"center"}},"✅",-1),C={style:{"text-align":"center"}},q=s("td",{style:{"text-align":"left"}},[s("code",null,"/v2/getDirectory/{Lid}")],-1),m=s("td",null,"获取文件",-1),B=s("td",{style:{"text-align":"center"}},"✅",-1),_={style:{"text-align":"center"}},v=s("td",{style:{"text-align":"left"}},[s("code",null,"/v2/getFiles/{Lid}/{page}")],-1),f=s("td",null,"搜索文件",-1),x=s("td",{style:{"text-align":"center"}},"✅",-1),D={style:{"text-align":"center"}},w=s("td",{style:{"text-align":"left"}},[s("code",null,"/v2/searchFile/{Lid}/{wd}")],-1),T=s("td",null,"依Id解析",-1),z=s("td",{style:{"text-align":"center"}},"✅",-1),A={style:{"text-align":"center"}},P=s("td",{style:{"text-align":"left"}},[s("code",null,"/v2/parseById/{type}/{Fid}")],-1),L=s("td",null,"依Url解析",-1),V=s("td",{style:{"text-align":"center"}},"✅",-1),I={style:{"text-align":"center"}},U=s("td",{style:{"text-align":"left"}},[s("code",null,"/v2/parseByUrl/{type}/{url}"),l(),s("br")],-1),j=n("",25),S=n("",6),R=n("",4);function J(N,G,Z,$,M,O){const i=e("Badge");return h(),p("div",null,[a(i,{type:"warning",text:"v1.0.5 - For beta",xmlns:"yes"}),r,o,d,s("table",null,[E,s("tbody",null,[s("tr",null,[c,u,s("td",g,[a(i,{type:"tip",text:"^1.0.5"})]),y]),s("tr",null,[b,F,s("td",C,[a(i,{type:"tip",text:"^1.0.5"})]),q]),s("tr",null,[m,B,s("td",_,[a(i,{type:"tip",text:"^1.0.5"})]),v]),s("tr",null,[f,x,s("td",D,[a(i,{type:"tip",text:"^1.0.5"})]),w]),s("tr",null,[T,z,s("td",A,[a(i,{type:"tip",text:"^1.0.5"})]),P]),s("tr",null,[L,V,s("td",I,[a(i,{type:"tip",text:"^1.0.5"})]),U])])]),j,a(i,{type:"danger",text:"注：V2版不支持需密码文件解析，有求可用V1版.",xmlns:"yes"}),S,a(i,{type:"danger",text:"注：V2版不支持需密码文件解析，有求可用V1版.",xmlns:"yes"}),R])}const H=t(k,[["render",J]]);export{W as __pageData,H as default};
