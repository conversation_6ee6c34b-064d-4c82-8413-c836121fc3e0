import{_ as t,c as e,o as l,a3 as a}from"./chunks/framework.D42TvibZ.js";const m=JSON.parse('{"title":"关于我 🧑‍💻","description":"","frontmatter":{"title":"关于我 🧑‍💻","layout":"home","hero":{"name":"Evan🕊️","text":"探索技术，追求卓越","tagline":"I love gale and spirits, loneliness and freedom.","actions":[{"theme":"brand","text":"了解更多 →","link":"#关于我"},{"theme":"alt","text":"项目概览 →","link":"#项目"}]}},"headers":[],"relativePath":"api-about.md","filePath":"api-about.md","lastUpdated":1740580269000}'),i={name:"api-about.md"},o=a("",25),s=[o];function r(n,c,d,p,u,h){return l(),e("div",null,s)}const b=t(i,[["render",r]]);export{m as __pageData,b as default};
