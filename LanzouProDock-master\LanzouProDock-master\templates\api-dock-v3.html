<!DOCTYPE html>
<html lang="en-US" dir="ltr">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>V3优享版接口 🆑 | Lanzou Pro API</title>
    <meta name="description" content="专业的蓝奏云文件API服务">
    <meta name="generator" content="VitePress v1.2.2">
    <link rel="preload stylesheet" href="/assets/style.B7bPjFih.css" as="style">
    
    <script type="module" src="/assets/app.DYadFD6L.js"></script>
    <link rel="preload" href="/assets/inter-roman-latin.Di8DUHzh.woff2" as="font" type="font/woff2" crossorigin="">
    <link rel="modulepreload" href="/assets/chunks/framework.D42TvibZ.js">
    <link rel="modulepreload" href="/assets/chunks/theme.5rVOLq5J.js">
    <link rel="modulepreload" href="/assets/api-dock-v3.md.BB2Hhw26.lean.js">
    <link rel="icon" href="logo.png">
    <meta name="theme-color" content="#ff416d">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <script id="check-dark-mode">(()=>{const e=localStorage.getItem("vitepress-theme-appearance")||"auto",a=window.matchMedia("(prefers-color-scheme: dark)").matches;(!e||e==="auto"?a:e==="dark")&&document.documentElement.classList.add("dark")})();</script>
    <script id="check-mac-os">document.documentElement.classList.toggle("mac",/Mac|iPhone|iPod|iPad/i.test(navigator.platform));</script>
  </head>
  <body>
    <div id="app"><div class="Layout" data-v-d8b57b2d><!--[--><!--]--><!--[--><span tabindex="-1" data-v-c8291ffa></span><a href="#VPContent" class="VPSkipLink visually-hidden" data-v-c8291ffa> Skip to content </a><!--]--><!----><header class="VPNav" data-v-d8b57b2d data-v-7ad780c2><div class="VPNavBar has-sidebar top" data-v-7ad780c2 data-v-844edcde><div class="wrapper" data-v-844edcde><div class="container" data-v-844edcde><div class="title" data-v-844edcde><div class="VPNavBarTitle has-sidebar" data-v-844edcde data-v-0ad69264><a class="title" href="/" data-v-0ad69264><!--[--><!--]--><!----><span data-v-0ad69264>Lanzou Pro API</span><!--[--><!--]--></a></div></div><div class="content" data-v-844edcde><div class="content-body" data-v-844edcde><!--[--><!--]--><div class="VPNavBarSearch search" data-v-844edcde><!--[--><!----><div id="local-search"><button type="button" class="DocSearch DocSearch-Button" aria-label="搜索文档"><span class="DocSearch-Button-Container"><span class="vp-icon DocSearch-Search-Icon"></span><span class="DocSearch-Button-Placeholder">搜索文档</span></span><span class="DocSearch-Button-Keys"><kbd class="DocSearch-Button-Key"></kbd><kbd class="DocSearch-Button-Key">K</kbd></span></button></div><!--]--></div><nav aria-labelledby="main-nav-aria-label" class="VPNavBarMenu menu" data-v-844edcde data-v-f732b5d0><span id="main-nav-aria-label" class="visually-hidden" data-v-f732b5d0>Main Navigation</span><!--[--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/" tabindex="0" data-v-f732b5d0 data-v-08fbf4b6><!--[--><span data-v-08fbf4b6>首页</span><!--]--></a><!--]--><!--[--><div class="VPFlyout VPNavBarMenuGroup" data-v-f732b5d0 data-v-af5898d3><button type="button" class="button" aria-haspopup="true" aria-expanded="false" data-v-af5898d3><span class="text" data-v-af5898d3><!----><span data-v-af5898d3>生态系统</span><span class="vpi-chevron-down text-icon" data-v-af5898d3></span></span></button><div class="menu" data-v-af5898d3><div class="VPMenu" data-v-af5898d3 data-v-e42ed9b3><div class="items" data-v-e42ed9b3><!--[--><!--[--><div class="VPMenuLink" data-v-e42ed9b3 data-v-f51f088d><a class="VPLink link vp-external-link-icon" href="https://uyclouds.com" target="_blank" rel="noreferrer" data-v-f51f088d><!--[-->🏢 UYCloud - 云计算<!--]--></a></div><!--]--><!--[--><div class="VPMenuLink" data-v-e42ed9b3 data-v-f51f088d><a class="VPLink link vp-external-link-icon" href="https://gitee.com/uyevan/wow-note-spring" target="_blank" rel="noreferrer" data-v-f51f088d><!--[-->📝 WowNote - 云笔记<!--]--></a></div><!--]--><!--[--><div class="VPMenuLink" data-v-e42ed9b3 data-v-f51f088d><a class="VPLink link vp-external-link-icon" href="https://gitee.com/uyevan/leying" target="_blank" rel="noreferrer" data-v-f51f088d><!--[-->📱 LeYing - 软件库<!--]--></a></div><!--]--><!--[--><div class="VPMenuLink" data-v-e42ed9b3 data-v-f51f088d><a class="VPLink link vp-external-link-icon" href="https://gitee.com/uyevan/uy-tool-club" target="_blank" rel="noreferrer" data-v-f51f088d><!--[-->🛠️ UyClub - 工具箱<!--]--></a></div><!--]--><!--[--><div class="VPMenuLink" data-v-e42ed9b3 data-v-f51f088d><a class="VPLink link vp-external-link-icon" href="https://gitee.com/uyevan/hambuk" target="_blank" rel="noreferrer" data-v-f51f088d><!--[-->🎬 HamBuk - 影视<!--]--></a></div><!--]--><!--]--></div><!--[--><!--]--></div></div></div><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/api-about.html" tabindex="0" data-v-f732b5d0 data-v-08fbf4b6><!--[--><span data-v-08fbf4b6>关于</span><!--]--></a><!--]--><!--]--></nav><!----><div class="VPNavBarAppearance appearance" data-v-844edcde data-v-283b26e9><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" title="Switch to dark theme" aria-checked="false" data-v-283b26e9 data-v-7df97737 data-v-b4ccac88><span class="check" data-v-b4ccac88><span class="icon" data-v-b4ccac88><!--[--><span class="vpi-sun sun" data-v-7df97737></span><span class="vpi-moon moon" data-v-7df97737></span><!--]--></span></span></button></div><div class="VPSocialLinks VPNavBarSocialLinks social-links" data-v-844edcde data-v-ef6192dc data-v-e71e869c><!--[--><a class="VPSocialLink no-icon" href="https://github.com/uyevan" aria-label="github" target="_blank" rel="noopener" data-v-e71e869c data-v-358b6670><span class="vpi-social-github" /></a><!--]--></div><div class="VPFlyout VPNavBarExtra extra" data-v-844edcde data-v-8e87c032 data-v-af5898d3><button type="button" class="button" aria-haspopup="true" aria-expanded="false" aria-label="extra navigation" data-v-af5898d3><span class="vpi-more-horizontal icon" data-v-af5898d3></span></button><div class="menu" data-v-af5898d3><div class="VPMenu" data-v-af5898d3 data-v-e42ed9b3><!----><!--[--><!--[--><!----><div class="group" data-v-8e87c032><div class="item appearance" data-v-8e87c032><p class="label" data-v-8e87c032>Appearance</p><div class="appearance-action" data-v-8e87c032><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" title="Switch to dark theme" aria-checked="false" data-v-8e87c032 data-v-7df97737 data-v-b4ccac88><span class="check" data-v-b4ccac88><span class="icon" data-v-b4ccac88><!--[--><span class="vpi-sun sun" data-v-7df97737></span><span class="vpi-moon moon" data-v-7df97737></span><!--]--></span></span></button></div></div></div><div class="group" data-v-8e87c032><div class="item social-links" data-v-8e87c032><div class="VPSocialLinks social-links-list" data-v-8e87c032 data-v-e71e869c><!--[--><a class="VPSocialLink no-icon" href="https://github.com/uyevan" aria-label="github" target="_blank" rel="noopener" data-v-e71e869c data-v-358b6670><span class="vpi-social-github" /></a><!--]--></div></div></div><!--]--><!--]--></div></div></div><!--[--><!--]--><button type="button" class="VPNavBarHamburger hamburger" aria-label="mobile navigation" aria-expanded="false" aria-controls="VPNavScreen" data-v-844edcde data-v-6bee1efd><span class="container" data-v-6bee1efd><span class="top" data-v-6bee1efd></span><span class="middle" data-v-6bee1efd></span><span class="bottom" data-v-6bee1efd></span></span></button></div></div></div></div><div class="divider" data-v-844edcde><div class="divider-line" data-v-844edcde></div></div></div><!----></header><div class="VPLocalNav has-sidebar empty" data-v-d8b57b2d data-v-2488c25a><div class="container" data-v-2488c25a><button class="menu" aria-expanded="false" aria-controls="VPSidebarNav" data-v-2488c25a><span class="vpi-align-left menu-icon" data-v-2488c25a></span><span class="menu-text" data-v-2488c25a>Menu</span></button><div class="VPLocalNavOutlineDropdown" style="--vp-vh:0px;" data-v-2488c25a data-v-883964e0><button data-v-883964e0>Return to top</button><!----></div></div></div><aside class="VPSidebar" data-v-d8b57b2d data-v-4871f9f5><div class="curtain" data-v-4871f9f5></div><nav class="nav" id="VPSidebarNav" aria-labelledby="sidebar-aria-label" tabindex="-1" data-v-4871f9f5><span class="visually-hidden" id="sidebar-aria-label" data-v-4871f9f5> Sidebar Navigation </span><!--[--><!--]--><!--[--><div class="group" data-v-4871f9f5><section class="VPSidebarItem level-0 collapsible" data-v-4871f9f5 data-v-c24f735a><div class="item" role="button" tabindex="0" data-v-c24f735a><div class="indicator" data-v-c24f735a></div><h2 class="text" data-v-c24f735a>蓝奏云普通版</h2><div class="caret" role="button" aria-label="toggle section" tabindex="0" data-v-c24f735a><span class="vpi-chevron-right caret-icon" data-v-c24f735a></span></div></div><div class="items" data-v-c24f735a><!--[--><div class="VPSidebarItem level-1 is-link" data-v-c24f735a data-v-c24f735a><div class="item" data-v-c24f735a><div class="indicator" data-v-c24f735a></div><a class="VPLink link link" href="/api-dock-v1.html" data-v-c24f735a><!--[--><p class="text" data-v-c24f735a>📌 V1区 (请求参数类)</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c24f735a data-v-c24f735a><div class="item" data-v-c24f735a><div class="indicator" data-v-c24f735a></div><a class="VPLink link link" href="/api-dock-v2.html" data-v-c24f735a><!--[--><p class="text" data-v-c24f735a>📌 V2区 (路径参数类)</p><!--]--></a><!----></div><!----></div><!--]--></div></section></div><div class="group" data-v-4871f9f5><section class="VPSidebarItem level-0 collapsible has-active" data-v-4871f9f5 data-v-c24f735a><div class="item" role="button" tabindex="0" data-v-c24f735a><div class="indicator" data-v-c24f735a></div><h2 class="text" data-v-c24f735a>蓝奏云优享版</h2><div class="caret" role="button" aria-label="toggle section" tabindex="0" data-v-c24f735a><span class="vpi-chevron-right caret-icon" data-v-c24f735a></span></div></div><div class="items" data-v-c24f735a><!--[--><div class="VPSidebarItem level-1 is-link" data-v-c24f735a data-v-c24f735a><div class="item" data-v-c24f735a><div class="indicator" data-v-c24f735a></div><a class="VPLink link link" href="/api-dock-v3.html" data-v-c24f735a><!--[--><p class="text" data-v-c24f735a>🚀 V3区 (路径参数类)</p><!--]--></a><!----></div><!----></div><!--]--></div></section></div><!--]--><!--[--><!--]--></nav></aside><div class="VPContent has-sidebar" id="VPContent" data-v-d8b57b2d data-v-9a6c75ad><div class="VPDoc has-sidebar has-aside" data-v-9a6c75ad data-v-e6f2a212><!--[--><!--]--><div class="container" data-v-e6f2a212><div class="aside" data-v-e6f2a212><div class="aside-curtain" data-v-e6f2a212></div><div class="aside-container" data-v-e6f2a212><div class="aside-content" data-v-e6f2a212><div class="VPDocAside" data-v-e6f2a212 data-v-cb998dce><!--[--><!--]--><!--[--><!--]--><nav aria-labelledby="doc-outline-aria-label" class="VPDocAsideOutline" data-v-cb998dce data-v-f610f197><div class="content" data-v-f610f197><div class="outline-marker" data-v-f610f197></div><div aria-level="2" class="outline-title" id="doc-outline-aria-label" role="heading" data-v-f610f197>本页大纲 🏷️</div><ul class="VPDocOutlineItem root" data-v-f610f197 data-v-53c99d69><!--[--><!--]--></ul></div></nav><!--[--><!--]--><div class="spacer" data-v-cb998dce></div><!--[--><!--]--><!----><!--[--><!--]--><!--[--><!--]--></div></div></div></div><div class="content" data-v-e6f2a212><div class="content-container" data-v-e6f2a212><!--[--><!--]--><main class="main" data-v-e6f2a212><div style="position:relative;" class="vp-doc _api-dock-v3" data-v-e6f2a212><div><span class="VPBadge warning" xmlns="yes"><!--[-->v1.0.5 - For beta<!--]--></span><h1 id="lanzou-pro-v3-优享版-接口列表" tabindex="-1">Lanzou Pro V3 优享版 接口列表 <a class="header-anchor" href="#lanzou-pro-v3-优享版-接口列表" aria-label="Permalink to &quot;Lanzou Pro V3 优享版 接口列表&quot;">​</a></h1><hr><p>本项目基于 Python3.10 开发、Web框架选用 Flask4.0、文档生成工具 VitePress.</p><table><thead><tr><th>API</th><th style="text-align:center;">状态</th><th style="text-align:center;">版本</th><th style="text-align:left;">路由</th></tr></thead><tbody><tr><td>获取文件夹ID</td><td style="text-align:center;">✅</td><td style="text-align:center;"><span class="VPBadge tip"><!--[-->^1.0.5<!--]--></span></td><td style="text-align:left;"><code>/v3/iGetFolderId/{shareId}/{Page}/{Limit}</code></td></tr><tr><td>获取文件列表</td><td style="text-align:center;">✅</td><td style="text-align:center;"><span class="VPBadge tip"><!--[-->^1.0.5<!--]--></span></td><td style="text-align:left;"><code>/v3/iGetFiles/{shareId}/{folderId}/{Page}/{Limit}</code></td></tr><tr><td>搜索文件</td><td style="text-align:center;">✅</td><td style="text-align:center;"><span class="VPBadge tip"><!--[-->^1.0.5<!--]--></span></td><td style="text-align:left;"><code>/v3/iSearchFile/{shareId}/{folderId}/{Wd}/{Page}/{Limit}</code></td></tr><tr><td>依文件Id解析</td><td style="text-align:center;">✅</td><td style="text-align:center;"><span class="VPBadge tip"><!--[-->^1.0.5<!--]--></span></td><td style="text-align:left;"><code>/v3/iParse/{fileId} (直链地址)</code></td></tr><tr><td>依文件Id解析</td><td style="text-align:center;">✅</td><td style="text-align:center;"><span class="VPBadge tip"><!--[-->^1.0.0<!--]--></span></td><td style="text-align:left;"><code>/v3/iParse301/{fileId} (重定向地址)</code></td></tr></tbody></table><h3 id="快速入门指南-🚀" tabindex="-1">快速入门指南 🚀 <a class="header-anchor" href="#快速入门指南-🚀" aria-label="Permalink to &quot;快速入门指南 🚀&quot;">​</a></h3><div class="tip custom-block"><p class="custom-block-title">参数获取流程</p><p>以下是获取各类 ID 的基本流程和说明：</p><h4 id="_1️⃣-分享id-shareid" tabindex="-1">1️⃣ 分享ID (shareId) <a class="header-anchor" href="#_1️⃣-分享id-shareid" aria-label="Permalink to &quot;1️⃣ 分享ID (shareId)&quot;">​</a></h4><ul><li>形如：<code>s0bJGkc</code></li><li>位置：优享版分享链接的最后部分</li><li>示例：<code>https://www.ilanzou.com/s/s0bJGkc</code> 中的 <code>s0bJGkc</code></li><li>用途：所有 API 的基础参数</li></ul><h4 id="_2️⃣-文件夹id-folderid" tabindex="-1">2️⃣ 文件夹ID (folderId) <a class="header-anchor" href="#_2️⃣-文件夹id-folderid" aria-label="Permalink to &quot;2️⃣ 文件夹ID (folderId)&quot;">​</a></h4><p>获取方式有两种：</p><ol><li>通过 API：调用 <code>iGetFolderId</code> 接口</li><li>手动获取：浏览器调试模式查看网络请求</li></ol><h4 id="_3️⃣-文件id-fileid" tabindex="-1">3️⃣ 文件ID (fileId) <a class="header-anchor" href="#_3️⃣-文件id-fileid" aria-label="Permalink to &quot;3️⃣ 文件ID (fileId)&quot;">​</a></h4><p>获取方式：</p><ol><li>调用 <code>iGetFiles</code> 接口（需要 shareId 和 folderId）</li><li>对于单文件分享，可直接通过 <code>iGetFolderId</code> 获取</li></ol></div><h3 id="接口文档-📇" tabindex="-1">接口文档 📇 <a class="header-anchor" href="#接口文档-📇" aria-label="Permalink to &quot;接口文档 📇&quot;">​</a></h3><h4 id="🤡-获取文件夹id-igetfolderid" tabindex="-1">🤡 获取文件夹ID：iGetFolderId <a class="header-anchor" href="#🤡-获取文件夹id-igetfolderid" aria-label="Permalink to &quot;🤡 获取文件夹ID：iGetFolderId&quot;">​</a></h4><ul><li><strong>路径</strong>：<code>/iGetFolderId</code></li><li><strong>请求方法</strong>：<code>GET</code></li><li><strong>请求参数</strong>： <ul><li><code>shareId</code>: 分享ID，优享版分享链接最后的字符串ID；如：<code>ilanzou.com\s\[shareId]</code></li><li><code>page</code>: 页码</li><li><code>limit</code>: 每页对应文件数量</li></ul></li><li><strong>返回实例</strong>：</li></ul><div class="language-json vp-adaptive-theme line-numbers-mode"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;code&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">200</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;folders&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: [</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    {</span></span>
<span class="line"><span style="--shiki-light:#B31D28;--shiki-dark:#FDAEB7;--shiki-light-font-style:italic;--shiki-dark-font-style:italic;">      ...</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileList&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: [</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        {</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">          &quot;fileType&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">2</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">          &quot;folderDesc&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;这是一个文件夹噢~&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">          &quot;folderIcon&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;assets/images/tab_file/icon/folder.png&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">          &quot;folderId&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">26269065</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">          &quot;folderName&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;个人作品另存图&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">          &quot;type&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">1</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">          &quot;updTime&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;2024-12-11 00:12:20&quot;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        }</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      ],</span></span>
<span class="line"><span style="--shiki-light:#B31D28;--shiki-dark:#FDAEB7;--shiki-light-font-style:italic;--shiki-dark-font-style:italic;">      ...</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    }</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  ],</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;status&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;获取成功&quot;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre><div class="line-numbers-wrapper" aria-hidden="true"><span class="line-number">1</span><br><span class="line-number">2</span><br><span class="line-number">3</span><br><span class="line-number">4</span><br><span class="line-number">5</span><br><span class="line-number">6</span><br><span class="line-number">7</span><br><span class="line-number">8</span><br><span class="line-number">9</span><br><span class="line-number">10</span><br><span class="line-number">11</span><br><span class="line-number">12</span><br><span class="line-number">13</span><br><span class="line-number">14</span><br><span class="line-number">15</span><br><span class="line-number">16</span><br><span class="line-number">17</span><br><span class="line-number">18</span><br><span class="line-number">19</span><br><span class="line-number">20</span><br><span class="line-number">21</span><br></div></div><div class="info custom-block"><p class="custom-block-title">路径说明</p><p>获取指定链接下的所有文件夹和文件信息。</p></div><div class="danger custom-block"><p class="custom-block-title">请求URL实例</p><p><a href="https://lanzou.uyclouds.com/v3/iGetFolderId/YRVyOZDp/1/30" target="_blank" rel="noreferrer">https://lanzou.uyclouds.com/v3/iGetFolderId/YRVyOZDp/1/30</a></p></div><hr><h4 id="📂-获取文件列表-igetfiles" tabindex="-1">📂 获取文件列表：iGetFiles <a class="header-anchor" href="#📂-获取文件列表-igetfiles" aria-label="Permalink to &quot;📂 获取文件列表：iGetFiles&quot;">​</a></h4><ul><li><strong>路径</strong>：<code>/iGetFiles</code></li><li><strong>请求方法</strong>：<code>GET</code></li><li><strong>请求参数</strong>： <ul><li><code>shareId</code>: 分享ID，优享版分享链接最后的字符串ID；如：<code>ilanzou.com\s\[shareId]</code></li><li><code>folderId</code>: 文件夹ID，每个分享的链接必定会有一个文件夹，可以通过第一个接口获取主文件夹ID；</li><li><code>page</code>: 页码</li><li><code>limit</code>: 每页对应文件数量</li></ul></li><li><strong>返回实例</strong>：</li></ul><div class="language-json vp-adaptive-theme line-numbers-mode"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;code&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">200</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;status&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;获取成功&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;files&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: [</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    {</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileComments&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileDownloads&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileIcon&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;https://image.webgetstore.com/202412110013/a553d4e4de08af5aae92bbc2f2253e7b/disk/icon/2024/12/10/115782/8145897345576944.rar&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileId&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">2631771977</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileLikes&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileName&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;head1.jpg&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileSaves&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileSize&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">116</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileStars&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">5.0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileType&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">1</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileUrl&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">null</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;iconId&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">16</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;name&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;head1.jpg&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;sortId&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">2631771977</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;type&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">1</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;updTime&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;2024-12-11 00:12:20&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;userId&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">2806070</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    },</span></span>
<span class="line"><span style="--shiki-light:#B31D28;--shiki-dark:#FDAEB7;--shiki-light-font-style:italic;--shiki-dark-font-style:italic;">    ...</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  ]</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre><div class="line-numbers-wrapper" aria-hidden="true"><span class="line-number">1</span><br><span class="line-number">2</span><br><span class="line-number">3</span><br><span class="line-number">4</span><br><span class="line-number">5</span><br><span class="line-number">6</span><br><span class="line-number">7</span><br><span class="line-number">8</span><br><span class="line-number">9</span><br><span class="line-number">10</span><br><span class="line-number">11</span><br><span class="line-number">12</span><br><span class="line-number">13</span><br><span class="line-number">14</span><br><span class="line-number">15</span><br><span class="line-number">16</span><br><span class="line-number">17</span><br><span class="line-number">18</span><br><span class="line-number">19</span><br><span class="line-number">20</span><br><span class="line-number">21</span><br><span class="line-number">22</span><br><span class="line-number">23</span><br><span class="line-number">24</span><br><span class="line-number">25</span><br><span class="line-number">26</span><br></div></div><div class="info custom-block"><p class="custom-block-title">路径说明</p><p>获取指定链接下的所有文件夹信息。</p></div><div class="danger custom-block"><p class="custom-block-title">请求URL实例</p><p><a href="https://lanzou.uyclouds.com/v3/iGetFiles/YRVyOZDp/26269065/1/30" target="_blank" rel="noreferrer">https://lanzou.uyclouds.com/v3/iGetFiles/YRVyOZDp/26269065/1/30</a></p></div><hr><h4 id="📄-搜索文件-isearchfile" tabindex="-1">📄 搜索文件：iSearchFile <a class="header-anchor" href="#📄-搜索文件-isearchfile" aria-label="Permalink to &quot;📄 搜索文件：iSearchFile&quot;">​</a></h4><ul><li><strong>路径</strong>：<code>/iSearchFile</code></li><li><strong>请求方法</strong>：<code>GET</code></li><li><strong>请求参数</strong>： <ul><li><code>shareId</code>: 分享ID，优享版分享链接最后的字符串ID；如：<code>ilanzou.com\s\[shareId]</code></li><li><code>folderId</code>: 文件夹ID，每个分享的链接必定会有一个文件夹，可以通过第一个接口获取主文件夹ID；</li><li><code>wd</code>: 搜索关键词；</li><li><code>page</code>: 页码</li><li><code>limit</code>: 每页对应文件数量</li></ul></li><li><strong>返回实例</strong>：</li></ul><div class="language-json vp-adaptive-theme line-numbers-mode"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;code&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">200</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;status&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;获取成功&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;files&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: [</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    {</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileComments&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileDownloads&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileIcon&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;https://image.webgetstore.com/202412110013/9deaaba8aab4bf44aec7056dabba0944/disk/icon/2024/12/10/115782/8145897264926055.rar&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileId&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">2631771975</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileLikes&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileName&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;head2.jpg&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileSaves&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileSize&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">94</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileStars&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">5.0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileType&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">1</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;fileUrl&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">null</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;iconId&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">16</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;name&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;head1.jpg&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;sortId&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">2631771975</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;type&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">1</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;updTime&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;2024-12-11 00:12:19&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;userId&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">2806070</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    },</span></span>
<span class="line"><span style="--shiki-light:#B31D28;--shiki-dark:#FDAEB7;--shiki-light-font-style:italic;--shiki-dark-font-style:italic;">    ...</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  ]</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre><div class="line-numbers-wrapper" aria-hidden="true"><span class="line-number">1</span><br><span class="line-number">2</span><br><span class="line-number">3</span><br><span class="line-number">4</span><br><span class="line-number">5</span><br><span class="line-number">6</span><br><span class="line-number">7</span><br><span class="line-number">8</span><br><span class="line-number">9</span><br><span class="line-number">10</span><br><span class="line-number">11</span><br><span class="line-number">12</span><br><span class="line-number">13</span><br><span class="line-number">14</span><br><span class="line-number">15</span><br><span class="line-number">16</span><br><span class="line-number">17</span><br><span class="line-number">18</span><br><span class="line-number">19</span><br><span class="line-number">20</span><br><span class="line-number">21</span><br><span class="line-number">22</span><br><span class="line-number">23</span><br><span class="line-number">24</span><br><span class="line-number">25</span><br><span class="line-number">26</span><br></div></div><div class="info custom-block"><p class="custom-block-title">路径说明</p><p>获取指定链接下的所有文件信息。</p></div><div class="danger custom-block"><p class="custom-block-title">请求URL实例</p><p><a href="https://lanzou.uyclouds.com/v3/iSearchFile/YRVyOZDp/26269065/head/1/30" target="_blank" rel="noreferrer">https://lanzou.uyclouds.com/v3/iSearchFile/YRVyOZDp/26269065/head/1/30</a></p></div><hr><h4 id="🆔-依文件id解析-返回文件下载直链地址-iparse" tabindex="-1">🆔 依文件Id解析（返回文件下载直链地址）：iParse <a class="header-anchor" href="#🆔-依文件id解析-返回文件下载直链地址-iparse" aria-label="Permalink to &quot;🆔 依文件Id解析（返回文件下载直链地址）：iParse&quot;">​</a></h4><span class="VPBadge danger" xmlns="yes"><!--[-->注：V3版不支持需密码文件解析，有求可用V1版.<!--]--></span><ul><li><strong>路径</strong>：<code>/iParse</code></li><li><strong>请求方法</strong>：<code>GET</code>、<code>POST</code></li><li><strong>请求参数</strong>： <ul><li><code>fileId</code>：需要解析的蓝奏云优享版文件fileId，必填。</li></ul></li><li><strong>返回实例</strong>：</li></ul><div class="language-json vp-adaptive-theme line-numbers-mode"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;code&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">200</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;status&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;解析成功&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;url&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;https://small4.bakstotre.com/0609263a24c226a103591742da632ad3/67bf28f9/2024/03/22/1e9f66ac045803bc84d2ffad7631bcf3.txt?fn=1-2224%E6%AD%BB%E7%81%B5%E6%B3%95%E5%B8%88.txt&quot;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre><div class="line-numbers-wrapper" aria-hidden="true"><span class="line-number">1</span><br><span class="line-number">2</span><br><span class="line-number">3</span><br><span class="line-number">4</span><br><span class="line-number">5</span><br></div></div><div class="danger custom-block"><p class="custom-block-title">请求URL实例</p><p><a href="https://lanzou.uyclouds.com/v3/iParse/277276043" target="_blank" rel="noreferrer">https://lanzou.uyclouds.com/v3/iParse/277276043</a></p></div><h4 id="🆔-依文件id解析-返回文件下载301重定向地址-iparse301" tabindex="-1">🆔 依文件Id解析（返回文件下载301重定向地址）：iParse301 <a class="header-anchor" href="#🆔-依文件id解析-返回文件下载301重定向地址-iparse301" aria-label="Permalink to &quot;🆔 依文件Id解析（返回文件下载301重定向地址）：iParse301&quot;">​</a></h4><ul><li><strong>路径</strong>：<code>/iParse301/{fileId}</code></li><li><strong>请求方法</strong>：<code>GET</code></li><li><strong>请求参数</strong>： <ul><li><code>fileId</code>：需要解析的蓝奏云优享版文件fileId，必填。</li></ul></li><li><strong>返回实例</strong>：</li></ul><div class="language-json vp-adaptive-theme line-numbers-mode"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;code&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">200</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;status&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;解析成功&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;url&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;https://api.ilanzou.com/unproved/file/redirect?downloadId=B303DB89B576288D147D469BF5318E2F&amp;enable=1&amp;devType=6&amp;uuid=7zEjxeRCxQrra560eTL8L&amp;timestamp=E038886D007E47A5BD0330267752D053&amp;auth=8C66AC14C8C450B78E8AEDF37A424C6BB5E140D21FC29563664D2B54BA404D5D&amp;shareId=8RTWCKF&quot;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre><div class="line-numbers-wrapper" aria-hidden="true"><span class="line-number">1</span><br><span class="line-number">2</span><br><span class="line-number">3</span><br><span class="line-number">4</span><br><span class="line-number">5</span><br></div></div><div class="danger custom-block"><p class="custom-block-title">请求URL实例</p><p><a href="https://lanzou.uyclouds.com/v3/iParse301/277276043" target="_blank" rel="noreferrer">https://lanzou.uyclouds.com/v3/iParse301/277276043</a></p></div><div class="info custom-block"><p class="custom-block-title">建议使用 <code>iParse</code> 接口，它会直接返回解析后的下载地址，而 <code>iParse301</code> 接口返回的是301重定向地址。</p></div><h3 id="📝-使用示例" tabindex="-1">📝 使用示例 <a class="header-anchor" href="#📝-使用示例" aria-label="Permalink to &quot;📝 使用示例&quot;">​</a></h3><div class="info custom-block"><p class="custom-block-title">文件夹分享场景</p><ul><li>第一步：获取文件夹ID</li></ul><p>GET /v3/iGetFolderId/s0bJGkc/1/30</p><ul><li>第二步：获取文件列表</li></ul><p>GET /v3/iGetFiles/s0bJGkc/26269065/1/30</p><ul><li>第三步：解析特定文件</li></ul><p>GET /v3/iParse/2631771977</p></div><div class="info custom-block"><p class="custom-block-title">单文件分享场景</p><ul><li>直接获取文件ID</li></ul><p>GET /v3/iGetFolderId/s0bJGkc/1/30</p><ul><li>解析下载链接</li></ul><p>GET /v3/iParse/2631771977</p></div></div></div></main><footer class="VPDocFooter" data-v-e6f2a212 data-v-5941af80><!--[--><!--]--><div class="edit-info" data-v-5941af80><div class="edit-link" data-v-5941af80><a class="VPLink link vp-external-link-icon no-icon edit-link-button" href="https://github.com/uyevan/LanzouProDock/edit/main/docs/api-dock-v3.md" target="_blank" rel="noreferrer" data-v-5941af80><!--[--><span class="vpi-square-pen edit-link-icon" data-v-5941af80></span> 在 GitHub 上编辑此页<!--]--></a></div><div class="last-updated" data-v-5941af80><p class="VPLastUpdated" data-v-5941af80 data-v-19a7ae4e>Last updated: <time datetime="2025-02-26T14:31:09.000Z" data-v-19a7ae4e></time></p></div></div><nav class="prev-next" aria-labelledby="doc-footer-aria-label" data-v-5941af80><span class="visually-hidden" id="doc-footer-aria-label" data-v-5941af80>Pager</span><div class="pager" data-v-5941af80><a class="VPLink link pager-link prev" href="/api-dock-v2.html" data-v-5941af80><!--[--><span class="desc" data-v-5941af80>Previous page</span><span class="title" data-v-5941af80>📌 V2区 (路径参数类)</span><!--]--></a></div><div class="pager" data-v-5941af80><!----></div></nav></footer><!--[--><!--]--></div></div></div><!--[--><!--]--></div></div><footer class="VPFooter has-sidebar" data-v-d8b57b2d data-v-566314d4><div class="container" data-v-566314d4><p class="message" data-v-566314d4>Copyright © 2025-present <a href="https://jfkj.xyz">Evan.</a></p><p class="copyright" data-v-566314d4>Lanzou Pro Api . <a href="https://beian.miit.gov.cn/" target="_blank">陕ICP备**********号-2</a></p></div></footer><!--[--><!--]--></div></div>
    <script>window.__VP_HASH_MAP__=JSON.parse("{\"readme.md\":\"BIVpoeSe\",\"api-about.md\":\"D3Spo74s\",\"index.md\":\"CuNlIUGS\",\"api-dock-v3.md\":\"BB2Hhw26\",\"api-dock-v1.md\":\"DX4dTZxR\",\"api-dock-v2.md\":\"BobUGIKQ\"}");window.__VP_SITE_DATA__=JSON.parse("{\"lang\":\"en-US\",\"dir\":\"ltr\",\"title\":\"Lanzou Pro API\",\"description\":\"专业的蓝奏云文件API服务\",\"base\":\"/\",\"head\":[],\"router\":{\"prefetchLinks\":true},\"appearance\":true,\"themeConfig\":{\"nav\":[{\"text\":\"首页\",\"link\":\"/\"},{\"text\":\"生态系统\",\"items\":[{\"text\":\"🏢 UYCloud - 云计算\",\"link\":\"https://uyclouds.com\"},{\"text\":\"📝 WowNote - 云笔记\",\"link\":\"https://gitee.com/uyevan/wow-note-spring\"},{\"text\":\"📱 LeYing - 软件库\",\"link\":\"https://gitee.com/uyevan/leying\"},{\"text\":\"🛠️ UyClub - 工具箱\",\"link\":\"https://gitee.com/uyevan/uy-tool-club\"},{\"text\":\"🎬 HamBuk - 影视\",\"link\":\"https://gitee.com/uyevan/hambuk\"}]},{\"text\":\"关于\",\"link\":\"/api-about\"}],\"sidebar\":[{\"text\":\"蓝奏云普通版\",\"collapsed\":false,\"items\":[{\"text\":\"📌 V1区 (请求参数类)\",\"link\":\"/api-dock-v1\"},{\"text\":\"📌 V2区 (路径参数类)\",\"link\":\"/api-dock-v2\"}]},{\"text\":\"蓝奏云优享版\",\"collapsed\":false,\"items\":[{\"text\":\"🚀 V3区 (路径参数类)\",\"link\":\"/api-dock-v3\"}]}],\"socialLinks\":[{\"icon\":\"github\",\"link\":\"https://github.com/uyevan\"}],\"footer\":{\"message\":\"Copyright © 2025-present <a href=\\\"https://jfkj.xyz\\\">Evan.</a>\",\"copyright\":\"Lanzou Pro Api . <a href=\\\"https://beian.miit.gov.cn/\\\" target=\\\"_blank\\\">陕ICP备**********号-2</a>\"},\"search\":{\"provider\":\"local\",\"options\":{\"translations\":{\"button\":{\"buttonText\":\"搜索文档\",\"buttonAriaLabel\":\"搜索文档\"},\"modal\":{\"noResultsText\":\"无法找到相关结果\",\"resetButtonTitle\":\"清除查询条件\",\"footer\":{\"selectText\":\"选择\",\"navigateText\":\"切换\"}}}}},\"outline\":{\"level\":\"deep\",\"label\":\"本页大纲 🏷️\"},\"editLink\":{\"pattern\":\"https://github.com/uyevan/LanzouProDock/edit/main/docs/:path\",\"text\":\"在 GitHub 上编辑此页\"}},\"locales\":{},\"scrollOffset\":134,\"cleanUrls\":false}");</script>
    
  </body>
</html>