import{_ as a,c as e,o,a3 as t}from"./chunks/framework.D42TvibZ.js";const p=JSON.parse('{"title":"LanzouDir","description":"","frontmatter":{},"headers":[],"relativePath":"README.md","filePath":"README.md","lastUpdated":1717346581000}'),r={name:"README.md"},l=t('<h1 id="lanzoudir" tabindex="-1">LanzouDir <a class="header-anchor" href="#lanzoudir" aria-label="Permalink to &quot;LanzouDir&quot;">​</a></h1><p>🥳蓝奏云文件夹解析，提供一个蓝奏云链接，解析并返回其中包含的文件夹内容或文件内容。</p><h5 id="实现功能" tabindex="-1">实现功能 <a class="header-anchor" href="#实现功能" aria-label="Permalink to &quot;实现功能&quot;">​</a></h5><ul><li>获取文件夹数据（Json）</li><li>获取文件信息（Json）</li><li>获取文件和文件夹信息（Json）</li><li>解析下载文件（Json）</li><li>文件搜索（Json）</li></ul><h5 id="faq" tabindex="-1">FAQ <a class="header-anchor" href="#faq" aria-label="Permalink to &quot;FAQ&quot;">​</a></h5><ul><li><a href="https://lanzou.uyclouds.com" target="_blank" rel="noreferrer">LanzouPro Dock</a></li></ul>',6),i=[l];function n(s,c,_,d,u,h){return o(),e("div",null,i)}const m=a(r,[["render",n]]);export{p as __pageData,m as default};
