import html
import re
from typing import List, Dict, Optional
import httpx
from nonebot import on_message, on_command
from nonebot.adapters.onebot.v11 import Event, Bot, GroupMessageEvent, Message, MessageSegment
from nonebot import logger
from nonebot.matcher import Matcher
from nonebot.params import CommandArg

# 导入插件管理器（假设存在）
try:
    from ..plugin_manager import PluginPriority, log_plugin_info, conditional_block
except ImportError:
    # 如果没有插件管理器，定义基本的优先级
    class PluginPriority:
        CRITICAL = 1
        HIGH = 5
        NORMAL = 10
        LOW = 15
    
    def log_plugin_info(name, desc, priority, block):
        logger.info(f"插件注册: {name} - {desc} (优先级: {priority}, 阻断: {block})")
    
    async def conditional_block(matcher, should_block):
        if should_block:
            matcher.stop_propagation()

# 蓝奏云API配置
LANZOU_API_BASE = "https://lanzou.uyclouds.com/v3"
SHARE_ID = "b03kvs06j"  # 从链接 https://baoz.lanzn.com/b03kvs06j 提取的分享ID

# 缓存文件夹ID，避免重复请求
folder_cache = {}
search_results_cache = {}

# 创建找书命令匹配器
search_book = on_command("找书", priority=PluginPriority.HIGH, block=True)
search_book_alt = on_command("搜书", priority=PluginPriority.HIGH, block=True)

# 记录插件信息
log_plugin_info("蓝奏云找书", "找书/搜书", PluginPriority.HIGH, True)

@search_book.handle()
@search_book_alt.handle()
async def handle_search_book(matcher: Matcher, bot: Bot, event: Event, args: Message = CommandArg()):
    """处理找书命令"""
    if not isinstance(event, GroupMessageEvent):
        await bot.send(event, "此功能仅支持群聊使用喵~")
        return
    
    # 获取搜索关键词
    keyword = args.extract_plain_text().strip()
    if not keyword:
        await bot.send(event, "请输入要搜索的书名喵~\n例如：找书 斗破苍穹")
        return
    
    await bot.send(event, f"正在搜索《{keyword}》，请稍候喵❤")
    
    try:
        # 搜索书籍
        search_results = await search_books(keyword)
        
        if not search_results:
            await bot.send(event, f"没有找到《{keyword}》相关的书籍喵~\n可以尝试换个关键词搜索")
            return
        
        # 构建搜索结果消息
        result_msg = f"找到 {len(search_results)} 本相关书籍喵~\n"
        result_msg += "=" * 30 + "\n"
        
        for i, book in enumerate(search_results[:10], 1):  # 最多显示10本书
            file_size = format_file_size(book.get('fileSize', 0))
            result_msg += f"{i}. 《{book['fileName']}》\n"
            result_msg += f"   大小: {file_size}\n"
            result_msg += f"   ID: {book['fileId']}\n"
            result_msg += "-" * 20 + "\n"
        
        if len(search_results) > 10:
            result_msg += f"还有 {len(search_results) - 10} 本书未显示...\n"
        
        result_msg += "\n回复 \"下载 书籍ID\" 即可下载喵~"
        
        # 缓存搜索结果
        search_results_cache[event.user_id] = search_results
        
        await bot.send(event, result_msg)
        
    except Exception as e:
        logger.error(f"搜索书籍时发生错误: {str(e)}", exc_info=True)
        await bot.send(event, f"搜索时出现错误喵~\n{str(e)}")

# 创建下载命令匹配器
download_book = on_command("下载", priority=PluginPriority.HIGH, block=True)

# 记录插件信息
log_plugin_info("蓝奏云下载", "下载", PluginPriority.HIGH, True)

@download_book.handle()
async def handle_download_book(matcher: Matcher, bot: Bot, event: Event, args: Message = CommandArg()):
    """处理下载命令"""
    if not isinstance(event, GroupMessageEvent):
        await bot.send(event, "此功能仅支持群聊使用喵~")
        return
    
    # 获取文件ID
    file_id = args.extract_plain_text().strip()
    if not file_id or not file_id.isdigit():
        await bot.send(event, "请输入正确的文件ID喵~\n例如：下载 123456789")
        return
    
    await bot.send(event, "正在获取下载链接，请稍候喵❤")
    
    try:
        # 获取下载链接
        download_url = await get_download_url(file_id)
        
        if not download_url:
            await bot.send(event, "获取下载链接失败喵~\n请检查文件ID是否正确")
            return
        
        # 查找对应的书籍信息
        book_info = None
        user_results = search_results_cache.get(event.user_id, [])
        for book in user_results:
            if str(book['fileId']) == file_id:
                book_info = book
                break
        
        # 构建下载消息
        if book_info:
            file_size = format_file_size(book_info.get('fileSize', 0))
            download_msg = Message([
                MessageSegment.at(event.user_id),
                MessageSegment.text(f"\n《{book_info['fileName']}》\n"),
                MessageSegment.text(f"大小: {file_size}\n"),
                MessageSegment.text("=" * 30 + "\n"),
                MessageSegment.text("下载链接获取成功喵❤\n"),
                MessageSegment.text(f"{download_url}")
            ])
        else:
            download_msg = Message([
                MessageSegment.at(event.user_id),
                MessageSegment.text(f"\n下载链接获取成功喵❤\n"),
                MessageSegment.text(f"{download_url}")
            ])
        
        await bot.send(event, download_msg)
        
    except Exception as e:
        logger.error(f"获取下载链接时发生错误: {str(e)}", exc_info=True)
        await bot.send(event, f"获取下载链接时出现错误喵~\n{str(e)}")

async def get_folder_id() -> Optional[str]:
    """获取主文件夹ID"""
    if 'main_folder' in folder_cache:
        return folder_cache['main_folder']
    
    try:
        async with httpx.AsyncClient(timeout=30) as client:
            url = f"{LANZOU_API_BASE}/iGetFolderId/{SHARE_ID}/1/30"
            response = await client.get(url)
            response.raise_for_status()
            data = response.json()
            
            if data.get('code') == 200 and 'folders' in data:
                folders = data['folders']
                if folders and len(folders) > 0:
                    # 获取第一个文件夹的文件列表
                    file_list = folders[0].get('fileList', [])
                    if file_list:
                        # 通常主文件夹ID是第一个文件夹的ID
                        main_folder_id = str(file_list[0].get('folderId', ''))
                        folder_cache['main_folder'] = main_folder_id
                        logger.info(f"获取到主文件夹ID: {main_folder_id}")
                        return main_folder_id
            
            logger.error(f"获取文件夹ID失败: {data}")
            return None
            
    except Exception as e:
        logger.error(f"获取文件夹ID时发生错误: {str(e)}")
        return None

async def search_books(keyword: str) -> List[Dict]:
    """搜索书籍"""
    folder_id = await get_folder_id()
    if not folder_id:
        raise Exception("无法获取文件夹ID")
    
    try:
        async with httpx.AsyncClient(timeout=30) as client:
            url = f"{LANZOU_API_BASE}/iSearchFile/{SHARE_ID}/{folder_id}/{keyword}/1/50"
            response = await client.get(url)
            response.raise_for_status()
            data = response.json()
            
            if data.get('code') == 200 and 'files' in data:
                files = data['files']
                logger.info(f"搜索到 {len(files)} 个文件")
                return files
            else:
                logger.warning(f"搜索返回异常: {data}")
                return []
                
    except Exception as e:
        logger.error(f"搜索书籍时发生错误: {str(e)}")
        raise

async def get_download_url(file_id: str) -> Optional[str]:
    """获取文件下载链接"""
    try:
        async with httpx.AsyncClient(timeout=30) as client:
            url = f"{LANZOU_API_BASE}/iParse/{file_id}"
            response = await client.get(url)
            response.raise_for_status()
            data = response.json()
            
            if data.get('code') == 200 and 'url' in data:
                download_url = data['url']
                logger.info(f"获取到下载链接: {download_url}")
                return download_url
            else:
                logger.error(f"获取下载链接失败: {data}")
                return None
                
    except Exception as e:
        logger.error(f"获取下载链接时发生错误: {str(e)}")
        return None

def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "未知"

    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"

# 创建浏览文件夹命令匹配器
browse_folder = on_command("浏览", priority=PluginPriority.NORMAL, block=True)
browse_folder_alt = on_command("书库", priority=PluginPriority.NORMAL, block=True)

# 记录插件信息
log_plugin_info("蓝奏云浏览", "浏览/书库", PluginPriority.NORMAL, True)

@browse_folder.handle()
@browse_folder_alt.handle()
async def handle_browse_folder(matcher: Matcher, bot: Bot, event: Event, args: Message = CommandArg()):
    """处理浏览文件夹命令"""
    if not isinstance(event, GroupMessageEvent):
        await bot.send(event, "此功能仅支持群聊使用喵~")
        return

    page = 1
    page_arg = args.extract_plain_text().strip()
    if page_arg and page_arg.isdigit():
        page = int(page_arg)
        if page < 1:
            page = 1

    await bot.send(event, f"正在获取书库第{page}页，请稍候喵❤")

    try:
        # 获取文件列表
        files = await get_files_list(page)

        if not files:
            await bot.send(event, f"第{page}页没有找到文件喵~")
            return

        # 构建文件列表消息
        result_msg = f"📚 书库第{page}页 ({len(files)}本书)\n"
        result_msg += "=" * 30 + "\n"

        for i, book in enumerate(files, 1):
            file_size = format_file_size(book.get('fileSize', 0))
            result_msg += f"{i}. 《{book['fileName']}》\n"
            result_msg += f"   大小: {file_size}\n"
            result_msg += f"   ID: {book['fileId']}\n"
            result_msg += "-" * 20 + "\n"

        result_msg += f"\n📖 回复 \"下载 书籍ID\" 下载\n"
        result_msg += f"📑 回复 \"浏览 {page + 1}\" 查看下一页"

        # 缓存浏览结果
        search_results_cache[event.user_id] = files

        await bot.send(event, result_msg)

    except Exception as e:
        logger.error(f"浏览文件夹时发生错误: {str(e)}", exc_info=True)
        await bot.send(event, f"浏览时出现错误喵~\n{str(e)}")

async def get_files_list(page: int = 1, limit: int = 20) -> List[Dict]:
    """获取文件列表"""
    folder_id = await get_folder_id()
    if not folder_id:
        raise Exception("无法获取文件夹ID")

    try:
        async with httpx.AsyncClient(timeout=30) as client:
            url = f"{LANZOU_API_BASE}/iGetFiles/{SHARE_ID}/{folder_id}/{page}/{limit}"
            response = await client.get(url)
            response.raise_for_status()
            data = response.json()

            if data.get('code') == 200 and 'files' in data:
                files = data['files']
                logger.info(f"获取到第{page}页 {len(files)} 个文件")
                return files
            else:
                logger.warning(f"获取文件列表返回异常: {data}")
                return []

    except Exception as e:
        logger.error(f"获取文件列表时发生错误: {str(e)}")
        raise

# 创建帮助命令匹配器
help_command = on_command("找书帮助", priority=PluginPriority.LOW, block=True)

# 记录插件信息
log_plugin_info("找书帮助", "找书帮助", PluginPriority.LOW, True)

@help_command.handle()
async def handle_help(matcher: Matcher, bot: Bot, event: Event):
    """显示帮助信息"""
    help_msg = """📚 蓝奏云找书插件帮助 📚

🔍 搜索功能：
• 找书 [书名] - 搜索指定书籍
• 搜书 [书名] - 搜索指定书籍（同上）

📖 浏览功能：
• 浏览 [页码] - 浏览书库文件列表
• 书库 [页码] - 浏览书库文件列表（同上）

⬇️ 下载功能：
• 下载 [文件ID] - 获取指定文件的下载链接

❓ 其他：
• 找书帮助 - 显示此帮助信息

💡 使用示例：
• 找书 斗破苍穹
• 浏览 1
• 下载 123456789

注意：文件ID可以从搜索或浏览结果中获取喵~"""

    await bot.send(event, help_msg)

# 创建智能搜索匹配器 - 识别"有没有xxx书"等自然语言
smart_search = on_message(priority=PluginPriority.NORMAL, block=False)

# 记录插件信息
log_plugin_info("智能找书", "自然语言搜索", PluginPriority.NORMAL, False)

@smart_search.handle()
async def handle_smart_search(matcher: Matcher, bot: Bot, event: Event):
    """智能搜索处理器"""
    if not isinstance(event, GroupMessageEvent):
        return

    raw_message = str(event.get_message()).strip()
    cleaned_message = html.unescape(raw_message)

    # 智能识别搜索意图的正则模式
    search_patterns = [
        r"有没有[《"]?([^《》""\n]+)[》"]?[这本]?书",
        r"找[一下]?[《"]?([^《》""\n]+)[》"]?[这本]?书",
        r"搜索[一下]?[《"]?([^《》""\n]+)[》"]?",
        r"求[《"]?([^《》""\n]+)[》"]?[这本]?书",
        r"[《"]([^《》""\n]+)[》"]有吗",
        r"想看[《"]?([^《》""\n]+)[》"]?",
        r"推荐.*[《"]([^《》""\n]+)[》"]",
    ]

    book_name = None
    for pattern in search_patterns:
        match = re.search(pattern, cleaned_message, re.IGNORECASE)
        if match:
            book_name = match.group(1).strip()
            # 过滤掉太短或太长的匹配
            if 2 <= len(book_name) <= 50:
                logger.info(f"智能搜索识别到书名: {book_name}")
                break

    if not book_name:
        return  # 没有识别到搜索意图，不处理

    # 阻断后续处理
    await conditional_block(matcher, True)

    await bot.send(event, f"检测到您想找《{book_name}》，正在搜索喵❤")

    try:
        # 执行搜索
        search_results = await search_books(book_name)

        if not search_results:
            await bot.send(event, f"很抱歉，没有找到《{book_name}》相关的书籍喵~\n可以尝试使用 \"找书 {book_name}\" 进行精确搜索")
            return

        # 构建搜索结果消息（简化版）
        if len(search_results) == 1:
            # 只有一本书，直接显示详细信息
            book = search_results[0]
            file_size = format_file_size(book.get('fileSize', 0))
            result_msg = f"找到了《{book['fileName']}》喵❤\n"
            result_msg += f"大小: {file_size}\n"
            result_msg += f"文件ID: {book['fileId']}\n"
            result_msg += f"回复 \"下载 {book['fileId']}\" 即可下载"
        else:
            # 多本书，显示前5本
            result_msg = f"找到 {len(search_results)} 本相关书籍喵~\n"
            result_msg += "=" * 25 + "\n"

            for i, book in enumerate(search_results[:5], 1):
                file_size = format_file_size(book.get('fileSize', 0))
                result_msg += f"{i}. 《{book['fileName']}》\n"
                result_msg += f"   大小: {file_size} | ID: {book['fileId']}\n"

            if len(search_results) > 5:
                result_msg += f"\n还有 {len(search_results) - 5} 本书，使用 \"找书 {book_name}\" 查看全部"

            result_msg += f"\n\n回复 \"下载 书籍ID\" 即可下载喵~"

        # 缓存搜索结果
        search_results_cache[event.user_id] = search_results

        await bot.send(event, result_msg)

    except Exception as e:
        logger.error(f"智能搜索时发生错误: {str(e)}", exc_info=True)
        await bot.send(event, f"搜索《{book_name}》时出现错误喵~\n可以尝试使用 \"找书 {book_name}\" 重新搜索")
