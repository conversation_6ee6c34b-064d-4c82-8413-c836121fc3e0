import{_ as l,D as t,c as e,I as a,j as s,a as p,a3 as n,o as h}from"./chunks/framework.D42TvibZ.js";const W=JSON.parse('{"title":"V1接口 🅰️","description":"","frontmatter":{"title":"V1接口 🅰️"},"headers":[],"relativePath":"api-dock-v1.md","filePath":"api-dock-v1.md","lastUpdated":1740580269000}'),k={name:"api-dock-v1.md"},r=s("h1",{id:"lanzou-pro-v1-接口列表",tabindex:"-1"},[p("Lanzou Pro V1 接口列表 "),s("a",{class:"header-anchor",href:"#lanzou-pro-v1-接口列表","aria-label":'Permalink to "Lanzou Pro V1 接口列表"'},"​")],-1),o=s("hr",null,null,-1),d=s("p",null,"本项目基于 Python3.10 开发、Web框架选用 Flask4.0、文档生成工具 VitePress.",-1),E=s("thead",null,[s("tr",null,[s("th",null,"API"),s("th",{style:{"text-align":"center"}},"状态"),s("th",{style:{"text-align":"center"}},"版本"),s("th",{style:{"text-align":"left"}},"路由")])],-1),c=s("td",null,"获取文件与目录",-1),u=s("td",{style:{"text-align":"center"}},"✅",-1),g={style:{"text-align":"center"}},y=s("td",{style:{"text-align":"left"}},[s("code",null,"/v1/getFilesAndDirectories?url={}&page={}")],-1),b=s("td",null,"获取目录",-1),F=s("td",{style:{"text-align":"center"}},"✅",-1),m={style:{"text-align":"center"}},C=s("td",{style:{"text-align":"left"}},[s("code",null,"/v1/getDirectory?url={}")],-1),q=s("td",null,"获取文件",-1),B=s("td",{style:{"text-align":"center"}},"✅",-1),_={style:{"text-align":"center"}},v=s("td",{style:{"text-align":"left"}},[s("code",null,"/v1/getFiles?url={}&page={}")],-1),f=s("td",null,"搜索文件",-1),x=s("td",{style:{"text-align":"center"}},"✅",-1),w={style:{"text-align":"center"}},z=s("td",{style:{"text-align":"left"}},[s("code",null,"/v1/searchFile?url={}&wd={}")],-1),D=s("td",null,"依Id解析",-1),T=s("td",{style:{"text-align":"center"}},"✅",-1),A={style:{"text-align":"center"}},P=s("td",{style:{"text-align":"left"}},[s("code",null,"/v1/parseById?type={}&id={}&pwd={}")],-1),I=s("td",null,"依Url解析",-1),U=s("td",{style:{"text-align":"center"}},"✅",-1),V={style:{"text-align":"center"}},j=s("td",{style:{"text-align":"left"}},[s("code",null,"/v1/parseByUrl?type={}&url={}&pwd={}")],-1),S=n("",25),R=n("",6),J=n("",4);function L(N,G,Z,$,M,O){const i=t("Badge");return h(),e("div",null,[a(i,{type:"warning",text:"v1.0.5 - For beta",xmlns:"yes"}),r,o,d,s("table",null,[E,s("tbody",null,[s("tr",null,[c,u,s("td",g,[a(i,{type:"tip",text:"^1.0.5"})]),y]),s("tr",null,[b,F,s("td",m,[a(i,{type:"tip",text:"^1.0.5"})]),C]),s("tr",null,[q,B,s("td",_,[a(i,{type:"tip",text:"^1.0.5"})]),v]),s("tr",null,[f,x,s("td",w,[a(i,{type:"tip",text:"^1.0.5"})]),z]),s("tr",null,[D,T,s("td",A,[a(i,{type:"tip",text:"^1.0.5"})]),P]),s("tr",null,[I,U,s("td",V,[a(i,{type:"tip",text:"^1.0.5"})]),j])])]),S,a(i,{type:"tip",text:"注：有密码则可以加上 ```&pwd=xxxx``` 参数，无则留空.",xmlns:"yes"}),R,a(i,{type:"tip",text:"注：有密码则可以加上 ```&pwd=xxxx``` 参数，无则留空.",xmlns:"yes"}),J])}const H=l(k,[["render",L]]);export{W as __pageData,H as default};
